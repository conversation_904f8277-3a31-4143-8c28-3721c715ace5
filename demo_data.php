<?php
/**
 * 演示數據生成器
 * KMS PC Receipt Maker
 */

require_once 'php/DatabaseMySQLi.php';
require_once 'php/Response.php';

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS PC Receipt Maker - 演示數據生成器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .result-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .result-success {
            background: #d4edda;
            color: #155724;
        }
        .result-error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="demo-card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            KMS PC Receipt Maker - 演示數據生成器
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>可用操作</h5>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-success" onclick="initPcParts()">
                                        <i class="fas fa-microchip me-1"></i>
                                        初始化電腦零件數據
                                    </button>
                                    <button class="btn btn-info" onclick="generateSampleReceipts()">
                                        <i class="fas fa-file-invoice me-1"></i>
                                        生成示例收據
                                    </button>
                                    <button class="btn btn-warning" onclick="generateSampleConfigs()">
                                        <i class="fas fa-cog me-1"></i>
                                        生成示例配置
                                    </button>
                                    <button class="btn btn-secondary" onclick="checkDataStatus()">
                                        <i class="fas fa-search me-1"></i>
                                        檢查數據狀態
                                    </button>
                                    <button class="btn btn-danger" onclick="clearAllData()">
                                        <i class="fas fa-trash me-1"></i>
                                        清空所有數據
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>操作結果</h5>
                                <div id="results" style="max-height: 400px; overflow-y: auto;">
                                    <div class="text-muted text-center">
                                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                                        <p>選擇左側操作開始</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-12">
                                <h5>快速鏈接</h5>
                                <div class="d-flex gap-2 flex-wrap">
                                    <a href="index.html" class="btn btn-primary">
                                        <i class="fas fa-home me-1"></i>
                                        主應用程序
                                    </a>
                                    <a href="test_all_functionality.html" class="btn btn-info">
                                        <i class="fas fa-vial me-1"></i>
                                        功能測試
                                    </a>
                                    <a href="php/setup_database.php" class="btn btn-success">
                                        <i class="fas fa-database me-1"></i>
                                        資料庫設置
                                    </a>
                                    <a href="php/test_connection.php" class="btn btn-warning" target="_blank">
                                        <i class="fas fa-plug me-1"></i>
                                        測試連接
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = `result-item result-${type}`;
            
            const icon = type === 'success' ? 'check-circle' : 
                        type === 'error' ? 'times-circle' : 'info-circle';
            
            resultItem.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${icon} me-2"></i>
                    <span>${message}</span>
                </div>
                <small class="text-muted">${new Date().toLocaleTimeString()}</small>
            `;
            
            resultsDiv.insertBefore(resultItem, resultsDiv.firstChild);
            
            // 限制結果數量
            while (resultsDiv.children.length > 10) {
                resultsDiv.removeChild(resultsDiv.lastChild);
            }
        }

        async function initPcParts() {
            addResult('正在初始化電腦零件數據...', 'info');
            
            try {
                const response = await fetch('php/init_pc_parts.php');
                const data = await response.json();
                
                if (data.success) {
                    addResult(`成功初始化 ${data.data.inserted_count} 個電腦零件項目`, 'success');
                } else {
                    addResult(`初始化失敗: ${data.message}`, 'error');
                }
            } catch (error) {
                addResult(`初始化錯誤: ${error.message}`, 'error');
            }
        }

        async function generateSampleReceipts() {
            addResult('正在生成示例收據...', 'info');
            
            const sampleReceipts = [
                {
                    customer: {
                        name: '張小明',
                        phone: '0912-345-678',
                        email: '<EMAIL>',
                        address: '台北市信義區信義路五段7號'
                    },
                    paymentMethod: 'cash',
                    items: [
                        { name: 'Intel Core i7-12700K', category: 'CPU', description: '12代處理器', quantity: 1, unitPrice: 12000, totalPrice: 12000 },
                        { name: 'ASUS ROG STRIX Z690-E', category: 'Motherboard', description: 'Z690主機板', quantity: 1, unitPrice: 8500, totalPrice: 8500 },
                        { name: 'Corsair Vengeance LPX 32GB', category: 'RAM', description: 'DDR4-3200記憶體', quantity: 1, unitPrice: 5500, totalPrice: 5500 }
                    ],
                    totals: { subtotal: 26000, discount: 1000, tax: 0, total: 25000 },
                    notes: '高性能遊戲配置'
                },
                {
                    customer: {
                        name: '李美華',
                        phone: '0987-654-321',
                        email: '<EMAIL>',
                        address: '新北市板橋區中山路一段161號'
                    },
                    paymentMethod: 'card',
                    items: [
                        { name: 'AMD Ryzen 5 5600X', category: 'CPU', description: 'Zen3處理器', quantity: 1, unitPrice: 7500, totalPrice: 7500 },
                        { name: 'MSI MAG B550 TOMAHAWK', category: 'Motherboard', description: 'B550主機板', quantity: 1, unitPrice: 4500, totalPrice: 4500 },
                        { name: 'NVIDIA GeForce RTX 3060', category: 'GPU', description: '12GB顯卡', quantity: 1, unitPrice: 15000, totalPrice: 15000 }
                    ],
                    totals: { subtotal: 27000, discount: 0, tax: 0, total: 27000 },
                    notes: '辦公遊戲兼用配置'
                }
            ];

            let successCount = 0;
            for (const receipt of sampleReceipts) {
                try {
                    const response = await fetch('php/save_receipt.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(receipt)
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        successCount++;
                    }
                } catch (error) {
                    console.error('Error saving receipt:', error);
                }
            }
            
            addResult(`成功生成 ${successCount} 個示例收據`, 'success');
        }

        async function generateSampleConfigs() {
            addResult('正在生成示例配置...', 'info');
            
            const sampleConfigs = [
                {
                    name: '高端遊戲配置',
                    customerInfo: { name: '遊戲玩家', phone: '0900-000-001', email: '<EMAIL>', address: '' },
                    paymentMethod: 'cash',
                    items: [
                        { name: 'Intel Core i9-12900K', category: 'CPU', quantity: 1, unitPrice: 18000, totalPrice: 18000 },
                        { name: 'NVIDIA GeForce RTX 4080', category: 'GPU', quantity: 1, unitPrice: 38000, totalPrice: 38000 }
                    ],
                    discountAmount: 2000,
                    taxRate: 0,
                    notes: '4K遊戲配置'
                },
                {
                    name: '辦公室配置',
                    customerInfo: { name: '辦公用戶', phone: '0900-000-002', email: '<EMAIL>', address: '' },
                    paymentMethod: 'transfer',
                    items: [
                        { name: 'AMD Ryzen 5 5600X', category: 'CPU', quantity: 1, unitPrice: 7500, totalPrice: 7500 },
                        { name: 'ASUS PRIME B550M-A', category: 'Motherboard', quantity: 1, unitPrice: 3200, totalPrice: 3200 }
                    ],
                    discountAmount: 0,
                    taxRate: 5,
                    notes: '穩定辦公配置'
                }
            ];

            let successCount = 0;
            for (const config of sampleConfigs) {
                try {
                    const response = await fetch('php/save_configuration.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(config)
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        successCount++;
                    }
                } catch (error) {
                    console.error('Error saving config:', error);
                }
            }
            
            addResult(`成功生成 ${successCount} 個示例配置`, 'success');
        }

        async function checkDataStatus() {
            addResult('正在檢查數據狀態...', 'info');
            
            try {
                // 檢查電腦零件
                const partsResponse = await fetch('php/get_pc_parts.php');
                const partsData = await partsResponse.json();
                
                if (partsData.success) {
                    addResult(`電腦零件: ${partsData.data.length} 個項目`, 'success');
                }
                
                // 檢查收據
                const receiptsResponse = await fetch('php/get_receipts.php');
                const receiptsData = await receiptsResponse.json();
                
                if (receiptsData.success) {
                    addResult(`收據記錄: ${receiptsData.data.total} 個收據`, 'success');
                }
                
                // 檢查配置
                const configsResponse = await fetch('php/get_configurations.php');
                const configsData = await configsResponse.json();
                
                if (configsData.success) {
                    addResult(`保存配置: ${configsData.data.length} 個配置`, 'success');
                }
                
            } catch (error) {
                addResult(`檢查數據狀態錯誤: ${error.message}`, 'error');
            }
        }

        async function clearAllData() {
            if (!confirm('確定要清空所有數據嗎？此操作無法撤銷！')) {
                return;
            }
            
            addResult('正在清空所有數據...', 'info');
            
            try {
                // 這裡需要實現清空數據的API
                addResult('數據清空功能需要額外實現', 'info');
            } catch (error) {
                addResult(`清空數據錯誤: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>