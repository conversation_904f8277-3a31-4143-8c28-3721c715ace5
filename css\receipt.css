/* KMS PC Receipt Maker - 收據樣式文件 */

/* 收據容器 */
.receipt {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 20px;
    line-height: 1.4;
}

/* 螢幕預覽時，使用 Letter 紙張實際比例顯示 (8.5in x 11in) */
@media screen {
    /* 預覽時強制所有文字 Arial 20px */
    .receipt * {
        font-family: Arial, Helvetica, sans-serif !important;
        font-size: 20px !important;
    }

    /* 讓右側預覽區可以滾動，以完整顯示 Letter 尺寸 */
    .receipt-preview {
        overflow: auto;
    }

    /* 在預覽區中，強制收據使用 Letter 寸法與 0.5in 內邊距 (模擬頁邊距) */
    .receipt-preview .receipt {
        width: 8.5in;
        min-height: 11in;
        max-width: none;
        padding: 0.5in; /* 與列印 @page margin 對齊的視覺邊距 */
        margin: 0 auto;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        background: #fff;
    }
}

/* 收據標題 */
.receipt-header {
    text-align: center;
    border-bottom: 2px solid #333;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.receipt-header .company-name {
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.receipt-header .company-info {
    font-size: inherit; /* 保持 20px */
    color: #666;
    line-height: 1.3;
}

/* 收據信息 */
.receipt-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

/* Receipt Number 不換行顯示完整 */
.receipt-info .receipt-number {
    white-space: nowrap;
}

.receipt-info .left,
.receipt-info .right {
    flex: 1;
}

.receipt-info .right {
    text-align: right;
}

.receipt-number {
    font-size: inherit;
    font-weight: bold;
    color: #0d6efd;
}

.receipt-date {
    color: #666;
    font-size: inherit;
}

/* 客戶信息 */
.customer-info {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.customer-info h6 {
    margin-bottom: 0.75rem;
    color: #333;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    padding-bottom: 0.25rem;
}

.customer-info .info-row:first-of-type {
    display: flex;
    gap: 0.5rem;
    align-items: baseline;
    white-space: nowrap;
}

.customer-info .info-label {
    font-weight: bold;
    width: 80px;
    color: #555;
}

.customer-info .info-value {
    flex: 1;
    color: #333;
}

/* 項目表格 */
.receipt-items {
    margin-bottom: 1.5rem;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.items-table th,
.items-table td {
    padding: 0.75rem 0.5rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.items-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #333;
    border-bottom: 2px solid #333;
}

.items-table .text-right {
    text-align: right;
}

.items-table .text-center {
    text-align: center;
}

.items-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 總計區域 */
.receipt-totals {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 2px solid #333;
}

.totals-table {
    width: 100%;
    max-width: 300px;
    margin-left: auto;
}

.totals-table td {
    padding: 0.5rem 1rem;
    border: none;
}

.totals-table .label {
    font-weight: bold;
    text-align: right;
    color: #555;
}

.totals-table .amount {
    text-align: right;
    font-family: inherit;
    font-weight: bold;
}

.totals-table .total-row {
    border-top: 1px solid #333;
    font-size: 1.1rem;
}

.totals-table .total-row .label,
.totals-table .total-row .amount {
    color: #333;
    font-weight: bold;
}

/* 付款信息 */
.payment-info {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.payment-method {
    font-weight: bold;
    color: #333;
}

.payment-status {
    color: #198754;
    font-weight: bold;
}

/* 備註 */
.receipt-notes {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    border-radius: 4px;
}

.receipt-notes h6 {
    margin-bottom: 0.5rem;
    color: #856404;
}

.receipt-notes p {
    margin: 0;
    color: #856404;
}

/* 收據底部 */
.receipt-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
    text-align: center;
    color: #666;
    font-size: 0.85rem;
}

.receipt-footer .thank-you {
    font-weight: bold;
    color: #333;
    margin-bottom: 1.5rem;
}

/* 簽名區域 */
.signature-section {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
    text-align: left; /* 覆蓋 footer 置中對齊 */
}

.signature-row {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 1.5rem;
    gap: 2rem;
}

.signature-field {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.signature-date {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 1rem;
}

.signature-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.signature-line {
    width: 150px;
    height: 1px;
    border-bottom: 1px solid #333;
    margin-bottom: 0.25rem;
}

/* 打印樣式 - Letter 尺寸 (8.5" x 11") */
@media print {
    @page {
        size: letter;
        margin: 0.5in;
    }

    body {
        margin: 0;
        padding: 0;
        background: white !important;
        font-size: 20px;
        line-height: 1.4;
        width: 7.5in; /* Letter width minus margins */
    }

    .receipt {
        box-shadow: none !important;
        border-radius: 0 !important;
        padding: 0.5rem !important;
        margin: 0 !important;
        max-width: none !important;
        width: 100% !important;
        page-break-inside: avoid;
    }

    /* Print uses the same Arial 20px as screen */
    .receipt,
    .receipt * {
        font-family: Arial, Helvetica, sans-serif !important;
        font-size: 20px !important;
    }

    .receipt-header .company-name {
        font-size: inherit;
    }

    .receipt-header .company-info {
        font-size: inherit;
    }

    .items-table th,
    .items-table td {
        padding: 0.5rem 0.25rem;
        font-size: inherit;
    }

    .totals-table td {
        padding: 0.25rem 0.5rem;
        font-size: inherit;
    }

    .customer-info,
    .payment-info,
    .receipt-notes {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    /* 隱藏不需要打印的元素 */
    .no-print,
    .btn,
    .navbar,
    .modal-header,
    .modal-footer {
        display: none !important;
    }

    /* 強制分頁 */
    .page-break {
        page-break-before: always;
    }

    /* 避免在表格行中間分頁 */
    .items-table tr {
        page-break-inside: avoid;
    }
}

/* 響應式收據設計 */
@media (max-width: 768px) {
    .receipt {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .receipt-header .company-name {
        font-size: 1.5rem;
    }

    .receipt-info {
        flex-direction: column;
        gap: 1rem;
    }

    .receipt-info .right {
        text-align: left;
    }

    .items-table {
        font-size: 0.8rem;
    }

    .items-table th,
    .items-table td {
        padding: 0.5rem 0.25rem;
    }

    .totals-table {
        max-width: 250px;
    }

    .payment-info {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .receipt {
        padding: 0.75rem;
        font-size: 0.85rem;
    }

    .receipt-header .company-name {
        font-size: 1.3rem;
    }

    .items-table {
        font-size: 0.75rem;
    }

    .customer-info .info-row {
        flex-direction: column;
    }

    .customer-info .info-label {
        width: auto;
        margin-bottom: 0.1rem;
    }
}

/* 收據動畫效果 */
.receipt-fade-in {
    animation: receiptFadeIn 0.5s ease-in-out;
}

@keyframes receiptFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 項目高亮效果 */
.receipt-item-highlight {
    background-color: #fff3cd !important;
    transition: background-color 0.3s ease;
}

/* 收據狀態指示器 */
.receipt-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
}

.receipt-status.paid {
    background-color: #d1edff;
    color: #0c5460;
}

.receipt-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.receipt-status.cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

/* Logo 拖拽和調整大小樣式 */
.receipt-logo {
    position: relative;
    width: 100%;
    margin-bottom: 1rem;
}

.draggable-logo {
    cursor: move;
    border: 2px dashed transparent;
    transition: border-color 0.3s ease;
    -webkit-user-select: none;
    user-select: none;
}

.draggable-logo:hover {
    border-color: #007bff;
}

.resize-handle {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #007bff;
    border: 2px solid white;
    border-radius: 50%;
    cursor: se-resize;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.draggable-logo:hover + .resize-handle,
.resize-handle:hover {
    opacity: 1;
}

/* 打印時隱藏拖拽控制項 */
@media print {
    .resize-handle {
        display: none !important;
    }

    .draggable-logo {
        border: none !important;
        cursor: default !important;
    }
}
