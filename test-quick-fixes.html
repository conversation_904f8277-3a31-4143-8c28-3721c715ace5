<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Test - All Fixes</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">Quick Test - All Fixes</h1>
        
        <!-- Quick Test Button -->
        <div class="text-center mb-4">
            <button type="button" class="btn btn-success btn-lg" onclick="runQuickTest()">
                <i class="fas fa-play me-2"></i>
                Run Quick Test (Auto-Generate + Items + Receipt)
            </button>
        </div>
        
        <!-- Customer Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Customer Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customerName" class="form-label">Customer Name</label>
                        <input type="text" class="form-control" id="customerName">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerPhone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="customerPhone">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="customerEmail">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerAddress" class="form-label">Address</label>
                        <input type="text" class="form-control" id="customerAddress">
                    </div>
                </div>
            </div>
        </div>

        <!-- Receipt Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Receipt Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="receiptNumber" class="form-label">Receipt Number</label>
                        <input type="text" class="form-control" id="receiptNumber" placeholder="Auto-generated">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="2"></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="discountAmount" class="form-label">Discount Amount</label>
                        <input type="number" class="form-control" id="discountAmount" value="0" min="0" step="0.01">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="taxRate" class="form-label">Tax Rate (%)</label>
                        <input type="number" class="form-control" id="taxRate" value="0" min="0" max="100" step="0.01">
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-primary" onclick="generateReceipt()">
                        <i class="fas fa-file-invoice me-1"></i>
                        Generate Receipt
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveReceipt()" id="saveBtn" disabled>
                        <i class="fas fa-save me-1"></i>
                        Save Receipt
                    </button>
                    <button type="button" class="btn btn-info" onclick="printReceipt()" id="printBtn" disabled>
                        <i class="fas fa-print me-1"></i>
                        Print Receipt
                    </button>
                </div>
            </div>
        </div>

        <!-- Receipt Preview -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Receipt Preview (No Category, No "Payment Method:" text)
                </h5>
            </div>
            <div class="card-body">
                <div id="receiptPreview">
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/main-new.js"></script>
    
    <script>
        function runQuickTest() {
            console.log('Starting quick test...');
            
            // Clear everything first
            if (window.ItemManager) {
                window.ItemManager.clearReceiptItems();
            }
            
            // Clear customer fields to trigger auto-generation
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';
            document.getElementById('customerEmail').value = '';
            document.getElementById('customerAddress').value = 'Test Address 123';
            document.getElementById('notes').value = 'Quick test receipt';
            
            // Add multiple test items to test multi-page printing
            if (window.ItemManager) {
                const testItems = [
                    { name: 'Intel Core i7-12700K', description: '12th Gen Processor', quantity: 1, unitPrice: 400, totalPrice: 400 },
                    { name: 'NVIDIA RTX 4070', description: 'Graphics Card', quantity: 1, unitPrice: 600, totalPrice: 600 },
                    { name: 'Corsair 32GB DDR4', description: 'Memory Kit', quantity: 2, unitPrice: 150, totalPrice: 300 },
                    { name: 'ASUS ROG Motherboard', description: 'Gaming Motherboard', quantity: 1, unitPrice: 300, totalPrice: 300 },
                    { name: 'Samsung 1TB SSD', description: 'NVMe Storage', quantity: 1, unitPrice: 120, totalPrice: 120 },
                    { name: 'Corsair 850W PSU', description: 'Modular Power Supply', quantity: 1, unitPrice: 180, totalPrice: 180 },
                    { name: 'NZXT H7 Case', description: 'Mid Tower Case', quantity: 1, unitPrice: 150, totalPrice: 150 },
                    { name: 'Noctua CPU Cooler', description: 'Air Cooler', quantity: 1, unitPrice: 90, totalPrice: 90 }
                ];
                
                testItems.forEach(item => {
                    window.ItemManager.addReceiptItem(item);
                });
                
                console.log('Added test items');
            }
            
            // Generate receipt (this should auto-fill customer info)
            if (window.generateReceipt) {
                window.generateReceipt();
                
                // Enable buttons
                document.getElementById('saveBtn').disabled = false;
                document.getElementById('printBtn').disabled = false;
                
                console.log('Generated receipt');
                
                // Show results
                setTimeout(() => {
                    const name = document.getElementById('customerName').value;
                    const phone = document.getElementById('customerPhone').value;
                    const email = document.getElementById('customerEmail').value;
                    
                    alert(`Quick Test Complete!\n\nAuto-generated customer info:\nName: ${name}\nPhone: ${phone}\nEmail: ${email}\n\nCheck the preview below:\n- No category shown\n- No "Payment Method:" text\n- 5 payment options only\n\nTry Print Receipt to test multi-page support!`);
                }, 500);
            }
        }
        
        // Override generateReceipt to enable buttons
        const originalGenerateReceipt = window.generateReceipt;
        window.generateReceipt = function() {
            if (originalGenerateReceipt) {
                originalGenerateReceipt();
                document.getElementById('saveBtn').disabled = false;
                document.getElementById('printBtn').disabled = false;
            }
        };
    </script>
</body>
</html>
