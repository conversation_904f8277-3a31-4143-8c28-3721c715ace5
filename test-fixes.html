<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS Receipt Maker - Fixes Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>KMS Receipt Maker - Fixes Test</h1>
        
        <!-- Test Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Test All Fixed Issues</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Preset & Add Item Tests</h6>
                        <button class="btn btn-select-preset btn-sm me-2" onclick="testShowPresetModal()">Test Preset Modal</button>
                        <button class="btn btn-primary btn-sm me-2" onclick="testShowAddItemModal()">Test Add Item Modal</button>
                        <button class="btn btn-success btn-sm" onclick="testAddPresetItem()">Add Test Preset Item</button>
                    </div>
                    <div class="col-md-6">
                        <h6>Receipt Generation Tests</h6>
                        <button class="btn btn-info btn-sm me-2" onclick="testGenerateReceipt()">Generate Receipt</button>
                        <button class="btn btn-warning btn-sm me-2" onclick="testDragDrop()">Test Drag & Drop</button>
                        <button class="btn btn-secondary btn-sm" onclick="clearTestResults()">Clear Results</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Customer Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customerName" class="form-label">Customer Name</label>
                        <input type="text" class="form-control" id="customerName" value="Test Customer">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerPhone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="customerPhone" value="555-1234">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="receiptNumber" class="form-label">Receipt Number</label>
                        <input type="text" class="form-control" id="receiptNumber" value="KMS-UltraVIP-0000001">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="receiptDate" class="form-label">Date</label>
                        <input type="datetime-local" class="form-control" id="receiptDate">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="discountAmount" class="form-label">Discount Amount</label>
                        <input type="number" class="form-control discount-input" id="discountAmount" min="0" step="0.01" value="0">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="taxRate" class="form-label">Tax Rate (%)</label>
                        <input type="number" class="form-control tax-input" id="taxRate" min="0" max="100" step="0.1" value="0">
                    </div>
                    <div class="col-12 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="2">Test receipt notes</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Item Section -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>
                        Add Item
                    </h5>
                    <button type="button" class="btn btn-select-preset btn-sm" onclick="showPresetModal()">
                        <i class="fas fa-list me-1"></i>
                        Select Preset
                    </button>
                </div>
            </div>
            <div class="card-body text-center">
                <p class="text-muted mb-3">Click the button below to add items to the receipt</p>
                <button type="button" class="btn btn-primary btn-lg" onclick="showAddItemModal()">
                    <i class="fas fa-plus-circle me-2"></i>
                    Add Item
                </button>
            </div>
        </div>

        <!-- Receipt Items -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Receipt Items
                    </h5>
                    <div class="d-flex gap-2">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="UIManager.toggleCompactView()" title="Toggle Compact View">
                                <i class="fas fa-compress-alt"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="UIManager.toggleDragAndDrop()" title="Toggle Drag & Drop">
                                <i class="fas fa-arrows-alt"></i>
                            </button>
                        </div>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllItems()">
                            <i class="fas fa-trash-alt me-1"></i>
                            Clear All
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="receiptItemsList" class="receipt-items-container">
                    <div class="receipt-items-empty">
                        <i class="fas fa-inbox"></i>
                        <p>No items added yet</p>
                    </div>
                </div>
                <div id="receiptTotals" class="mt-3"></div>
            </div>
        </div>

        <!-- Receipt Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Receipt Preview
                </h5>
            </div>
            <div class="card-body">
                <div id="receiptPreview">
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here</p>
                    </div>
                </div>
                <div id="previewActions" class="d-none mt-3">
                    <button type="button" class="btn btn-success btn-sm" onclick="saveReceipt()">
                        <i class="fas fa-save me-1"></i>
                        Save Receipt
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="printReceipt()">
                        <i class="fas fa-print me-1"></i>
                        Print Receipt
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card">
            <div class="card-header">
                <h5>Test Results</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">Test results will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Item Modal (copied from main index.html) -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-plus-circle me-2"></i>
                        Add Item
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addItemModalForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Item Name *</label>
                                <input type="text" class="form-control" id="modalItemName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Category</label>
                                <select class="form-select" id="modalItemCategory">
                                    <option value="PC Case">PC Case</option>
                                    <option value="CPU">CPU</option>
                                    <option value="GPU">GPU</option>
                                    <option value="RAM">RAM</option>
                                    <option value="Storage">Storage</option>
                                    <option value="Motherboard">Motherboard</option>
                                    <option value="PSU">PSU</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" id="modalItemDescription" rows="2"></textarea>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Quantity *</label>
                                <input type="number" class="form-control" id="modalItemQuantity" min="1" value="1" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Original Price</label>
                                <input type="number" class="form-control" id="modalItemOriginalPrice" min="0" step="0.01">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Special Price *</label>
                                <input type="number" class="form-control" id="modalItemSpecialPrice" min="0" step="0.01" required>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="modalItemHidePrice">
                                    <label class="form-check-label" for="modalItemHidePrice">
                                        Hide Price (Show N/A on receipt)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="confirmAddModalItem()">
                        <i class="fas fa-check me-1"></i>
                        Confirm Add
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS - Load in order -->
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    
    <!-- Core modules (load first) -->
    <script src="js/core/app-initializer.js"></script>
    
    <!-- Feature modules -->
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    
    <!-- Main coordinator -->
    <script src="js/main-new.js"></script>
    
    <!-- Legacy modules that depend on new system -->
    <script src="js/preset-manager.js"></script>
    <script src="js/item-manager.js"></script>
    <script src="js/config-manager.js"></script>
    <script src="js/receipt.js"></script>

    <!-- Test Functions -->
    <script>
        let testCounter = 0;

        function logTest(message, success = true) {
            testCounter++;
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = success ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>';
            resultsDiv.innerHTML += `<div class="mb-1">${statusIcon} [${timestamp}] Test ${testCounter}: ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearTestResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-muted">Test results will appear here...</p>';
            testCounter = 0;
        }

        function testShowPresetModal() {
            try {
                showPresetModal();
                logTest('Preset modal opened successfully');
            } catch (error) {
                logTest(`Failed to open preset modal: ${error.message}`, false);
            }
        }

        function testShowAddItemModal() {
            try {
                showAddItemModal();
                logTest('Add item modal opened successfully');
            } catch (error) {
                logTest(`Failed to open add item modal: ${error.message}`, false);
            }
        }

        function testAddPresetItem() {
            try {
                const testItem = {
                    id: 999,
                    name: 'Test GPU RTX 4090',
                    category: 'GPU',
                    description: 'High-end graphics card',
                    original_price: 1999.99,
                    special_price: 1599.99,
                    default_price: 1599.99
                };
                addItemToReceipt(testItem);
                logTest('Test preset item added successfully');
            } catch (error) {
                logTest(`Failed to add preset item: ${error.message}`, false);
            }
        }

        function testGenerateReceipt() {
            try {
                generateReceipt();
                logTest('Receipt generated successfully');
            } catch (error) {
                logTest(`Failed to generate receipt: ${error.message}`, false);
            }
        }

        function testDragDrop() {
            try {
                if (window.ItemManager && window.ItemManager.isDragDropEnabled !== undefined) {
                    logTest(`Drag & drop is ${window.ItemManager.isDragDropEnabled ? 'enabled' : 'disabled'}`);
                } else {
                    logTest('Drag & drop status unknown', false);
                }
            } catch (error) {
                logTest(`Failed to check drag & drop: ${error.message}`, false);
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Test page loaded');
            
            // Set current date
            const now = new Date();
            const dateString = now.toISOString().slice(0, 16);
            document.getElementById('receiptDate').value = dateString;
            
            // Test module availability after a short delay
            setTimeout(() => {
                logTest(`AppInitializer available: ${typeof window.AppInitializer !== 'undefined'}`);
                logTest(`ItemManager available: ${typeof window.ItemManager !== 'undefined'}`);
                logTest(`ReceiptGenerator available: ${typeof window.ReceiptGenerator !== 'undefined'}`);
                logTest(`UIManager available: ${typeof window.UIManager !== 'undefined'}`);
                logTest(`showAddItemModal function available: ${typeof window.showAddItemModal === 'function'}`);
                logTest(`confirmAddModalItem function available: ${typeof window.confirmAddModalItem === 'function'}`);
            }, 500);
        });
    </script>
</body>
</html>
