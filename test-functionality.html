<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS Receipt Maker - Test Page</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>KMS Receipt Maker - Functionality Test</h1>
        
        <!-- Test Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Test Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Item Management Tests</h6>
                        <button class="btn btn-primary btn-sm me-2" onclick="testAddItem()">Add Test Item</button>
                        <button class="btn btn-secondary btn-sm me-2" onclick="testClearItems()">Clear Items</button>
                        <button class="btn btn-info btn-sm" onclick="testToggleView()">Toggle Compact View</button>
                    </div>
                    <div class="col-md-6">
                        <h6>Receipt Generation Tests</h6>
                        <button class="btn btn-success btn-sm me-2" onclick="testGenerateReceipt()">Generate Receipt</button>
                        <button class="btn btn-warning btn-sm me-2" onclick="testPrintReceipt()">Test Print</button>
                        <button class="btn btn-danger btn-sm" onclick="testShowMessage()">Test Message</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Customer Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customerName" class="form-label">Customer Name</label>
                        <input type="text" class="form-control" id="customerName" value="John Doe">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerPhone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="customerPhone" value="555-1234">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="customerEmail" value="<EMAIL>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerAddress" class="form-label">Address</label>
                        <textarea class="form-control" id="customerAddress" rows="2">123 Main St, City, State</textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="receiptNumber" class="form-label">Receipt Number</label>
                        <input type="text" class="form-control" id="receiptNumber" value="KMS-UltraVIP-0000001">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="receiptDate" class="form-label">Date</label>
                        <input type="datetime-local" class="form-control" id="receiptDate">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="discountAmount" class="form-label">Discount Amount</label>
                        <input type="number" class="form-control discount-input" id="discountAmount" min="0" step="0.01" value="0">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="taxRate" class="form-label">Tax Rate (%)</label>
                        <input type="number" class="form-control tax-input" id="taxRate" min="0" max="100" step="0.1" value="0">
                    </div>
                    <div class="col-12 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="2">Test receipt notes</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Receipt Items -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Receipt Items
                    </h5>
                    <div class="d-flex gap-2">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="UIManager.toggleCompactView()" title="Toggle Compact View">
                                <i class="fas fa-compress-alt"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="UIManager.toggleDragAndDrop()" title="Toggle Drag & Drop">
                                <i class="fas fa-arrows-alt"></i>
                            </button>
                        </div>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllItems()">
                            <i class="fas fa-trash-alt me-1"></i>
                            Clear All
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="receiptItemsList" class="receipt-items-container">
                    <div class="receipt-items-empty">
                        <i class="fas fa-inbox"></i>
                        <p>No items added yet</p>
                    </div>
                </div>
                <div id="receiptTotals" class="mt-3"></div>
            </div>
        </div>

        <!-- Receipt Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Receipt Preview
                </h5>
            </div>
            <div class="card-body">
                <div id="receiptPreview">
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here</p>
                    </div>
                </div>
                <div id="previewActions" class="d-none mt-3">
                    <button type="button" class="btn btn-success btn-sm" onclick="saveReceipt()">
                        <i class="fas fa-save me-1"></i>
                        Save Receipt
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="printReceipt()">
                        <i class="fas fa-print me-1"></i>
                        Print Receipt
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card">
            <div class="card-header">
                <h5>Test Results</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">Test results will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Core modules -->
    <script src="js/core/app-initializer.js"></script>
    
    <!-- Feature modules -->
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    
    <!-- Main coordinator -->
    <script src="js/main-new.js"></script>

    <!-- Test Functions -->
    <script>
        let testCounter = 0;

        function logTest(message, success = true) {
            testCounter++;
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = success ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>';
            resultsDiv.innerHTML += `<div class="mb-1">${statusIcon} [${timestamp}] Test ${testCounter}: ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function testAddItem() {
            try {
                const testItem = {
                    name: `Test Item ${Date.now()}`,
                    category: 'Test Category',
                    description: 'This is a test item',
                    default_price: 99.99
                };
                addItemToReceipt(testItem);
                logTest('Item added successfully');
            } catch (error) {
                logTest(`Failed to add item: ${error.message}`, false);
            }
        }

        function testClearItems() {
            try {
                clearAllItems();
                logTest('Items cleared successfully');
            } catch (error) {
                logTest(`Failed to clear items: ${error.message}`, false);
            }
        }

        function testToggleView() {
            try {
                if (window.UIManager) {
                    window.UIManager.toggleCompactView();
                    logTest('Compact view toggled successfully');
                } else {
                    logTest('UIManager not available', false);
                }
            } catch (error) {
                logTest(`Failed to toggle view: ${error.message}`, false);
            }
        }

        function testGenerateReceipt() {
            try {
                generateReceipt();
                logTest('Receipt generated successfully');
            } catch (error) {
                logTest(`Failed to generate receipt: ${error.message}`, false);
            }
        }

        function testPrintReceipt() {
            try {
                // Don't actually print, just test the function
                if (window.ReceiptGenerator) {
                    logTest('Print function available');
                } else {
                    logTest('ReceiptGenerator not available', false);
                }
            } catch (error) {
                logTest(`Print test failed: ${error.message}`, false);
            }
        }

        function testShowMessage() {
            try {
                showMessage('This is a test message!', 'info');
                logTest('Message displayed successfully');
            } catch (error) {
                logTest(`Failed to show message: ${error.message}`, false);
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Test page loaded');
            
            // Set current date
            const now = new Date();
            const dateString = now.toISOString().slice(0, 16);
            document.getElementById('receiptDate').value = dateString;
            
            // Test module availability
            setTimeout(() => {
                logTest(`AppInitializer available: ${typeof window.AppInitializer !== 'undefined'}`);
                logTest(`ItemManager available: ${typeof window.ItemManager !== 'undefined'}`);
                logTest(`ReceiptGenerator available: ${typeof window.ReceiptGenerator !== 'undefined'}`);
                logTest(`UIManager available: ${typeof window.UIManager !== 'undefined'}`);
            }, 200);
        });
    </script>
</body>
</html>
