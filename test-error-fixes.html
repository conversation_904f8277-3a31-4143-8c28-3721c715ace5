<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS Receipt Maker - Error Fixes Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>KMS Receipt Maker - Error Fixes Test</h1>
        
        <!-- Error Status Display -->
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5>JavaScript Error Monitor</h5>
            </div>
            <div class="card-body">
                <div id="errorDisplay">
                    <p class="text-success">✅ No JavaScript errors detected</p>
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Function Availability Tests</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Core Functions</h6>
                        <button class="btn btn-primary btn-sm me-2" onclick="testAddItem()">Test Add Item</button>
                        <button class="btn btn-success btn-sm me-2" onclick="testEditFunction()">Test Edit Function</button>
                        <button class="btn btn-info btn-sm" onclick="testPresetFunction()">Test Preset Function</button>
                    </div>
                    <div class="col-md-6">
                        <h6>Module Tests</h6>
                        <button class="btn btn-warning btn-sm me-2" onclick="testModuleAvailability()">Test Modules</button>
                        <button class="btn btn-secondary btn-sm me-2" onclick="testMessageFunction()">Test Messages</button>
                        <button class="btn btn-danger btn-sm" onclick="clearTestResults()">Clear Results</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Customer Information (Optional)</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customerName" class="form-label">Customer Name</label>
                        <input type="text" class="form-control" id="customerName" placeholder="Optional">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="receiptNumber" class="form-label">Receipt Number</label>
                        <input type="text" class="form-control" id="receiptNumber" value="KMS-UltraVIP-0000001">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="discountAmount" class="form-label">Discount Amount</label>
                        <input type="number" class="form-control discount-input" id="discountAmount" min="0" step="0.01" value="0">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="taxRate" class="form-label">Tax Rate (%)</label>
                        <input type="number" class="form-control tax-input" id="taxRate" min="0" max="100" step="0.1" value="0">
                    </div>
                </div>
            </div>
        </div>

        <!-- Receipt Items -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Receipt Items
                    </h5>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllItems()">
                        <i class="fas fa-trash-alt me-1"></i>
                        Clear All
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="receiptItemsList" class="receipt-items-container">
                    <div class="receipt-items-empty">
                        <i class="fas fa-inbox"></i>
                        <p>No items added yet</p>
                    </div>
                </div>
                <div id="receiptTotals" class="mt-3"></div>
            </div>
        </div>

        <!-- Receipt Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Receipt Preview
                </h5>
            </div>
            <div class="card-body">
                <div id="receiptPreview">
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here</p>
                    </div>
                </div>
                <div id="previewActions" class="d-none mt-3">
                    <button type="button" class="btn btn-success btn-sm" onclick="saveReceipt()">
                        <i class="fas fa-save me-1"></i>
                        Save Receipt
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="printReceipt()">
                        <i class="fas fa-print me-1"></i>
                        Print Receipt
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card">
            <div class="card-header">
                <h5>Test Results</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">Test results will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Error Monitoring -->
    <script>
        let errorCount = 0;
        const originalConsoleError = console.error;
        
        window.addEventListener('error', function(e) {
            errorCount++;
            const errorDisplay = document.getElementById('errorDisplay');
            errorDisplay.innerHTML = `<p class="text-danger">❌ JavaScript Error ${errorCount}: ${e.message}</p>`;
        });
        
        console.error = function(...args) {
            errorCount++;
            const errorDisplay = document.getElementById('errorDisplay');
            if (errorDisplay) {
                errorDisplay.innerHTML = `<p class="text-danger">❌ Console Error ${errorCount}: ${args.join(' ')}</p>`;
            }
            originalConsoleError.apply(console, args);
        };
    </script>
    
    <!-- Custom JS - Load in order -->
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    
    <!-- Core modules (load first) -->
    <script src="js/core/app-initializer.js"></script>
    
    <!-- Feature modules -->
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    
    <!-- Main coordinator -->
    <script src="js/main-new.js"></script>
    
    <!-- Legacy modules that depend on new system -->
    <script src="js/preset-manager.js"></script>
    <script src="js/config-manager.js"></script>
    <script src="js/receipt.js"></script>

    <!-- Test Functions -->
    <script>
        let testCounter = 0;

        function logTest(message, success = true) {
            testCounter++;
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = success ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>';
            resultsDiv.innerHTML += `<div class="mb-1">${statusIcon} [${timestamp}] Test ${testCounter}: ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearTestResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-muted">Test results will appear here...</p>';
            testCounter = 0;
        }

        function testAddItem() {
            try {
                const testItem = {
                    id: Date.now(),
                    name: 'Test Graphics Card',
                    category: 'GPU',
                    description: 'Test item for error checking',
                    original_price: 999.99,
                    special_price: 799.99,
                    default_price: 799.99
                };
                addItemToReceipt(testItem);
                logTest('Add item function works correctly');
            } catch (error) {
                logTest(`Add item failed: ${error.message}`, false);
            }
        }

        function testEditFunction() {
            try {
                const items = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
                if (items.length > 0) {
                    if (typeof editItem === 'function') {
                        editItem(items[0].id);
                        logTest('Edit item function works correctly');
                    } else {
                        logTest('Edit item function not available', false);
                    }
                } else {
                    logTest('No items to edit - add an item first', false);
                }
            } catch (error) {
                logTest(`Edit function failed: ${error.message}`, false);
            }
        }

        function testPresetFunction() {
            try {
                if (typeof showPresetModal === 'function') {
                    logTest('Preset modal function available');
                } else {
                    logTest('Preset modal function not available', false);
                }
            } catch (error) {
                logTest(`Preset function failed: ${error.message}`, false);
            }
        }

        function testModuleAvailability() {
            logTest(`AppInitializer: ${typeof window.AppInitializer !== 'undefined'}`);
            logTest(`ItemManager: ${typeof window.ItemManager !== 'undefined'}`);
            logTest(`ReceiptGenerator: ${typeof window.ReceiptGenerator !== 'undefined'}`);
            logTest(`UIManager: ${typeof window.UIManager !== 'undefined'}`);
            logTest(`presetManager: ${typeof window.presetManager !== 'undefined'}`);
        }

        function testMessageFunction() {
            try {
                if (typeof showMessage === 'function') {
                    showMessage('Test message function works!', 'info');
                    logTest('Message function works correctly');
                } else {
                    logTest('Message function not available', false);
                }
            } catch (error) {
                logTest(`Message function failed: ${error.message}`, false);
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Error fixes test page loaded');
            
            // Test module availability after a short delay
            setTimeout(() => {
                testModuleAvailability();
                
                // Check for specific functions
                logTest(`editItem function: ${typeof editItem === 'function'}`);
                logTest(`addItemToReceipt function: ${typeof addItemToReceipt === 'function'}`);
                logTest(`showMessage function: ${typeof showMessage === 'function'}`);
                logTest(`generateReceipt function: ${typeof generateReceipt === 'function'}`);
                
                if (errorCount === 0) {
                    logTest('✅ No JavaScript errors detected during initialization');
                } else {
                    logTest(`❌ ${errorCount} JavaScript errors detected`, false);
                }
            }, 1000);
        });
    </script>
</body>
</html>
