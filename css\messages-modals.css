/**
 * Messages and Modals Styles
 * Toast messages, alerts, and modal dialogs
 */

/* Toast Messages */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
    animation: slideInRight 0.3s ease-out;
}

.toast-message.removing {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Alert Variants */
.alert-success {
    background-color: #d1e7dd;
    border-color: #badbcc;
    color: #0f5132;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #055160;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #664d03;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c2c7;
    color: #842029;
}

/* Modal Enhancements */
.modal-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    border-bottom: none;
    border-radius: 0.375rem 0.375rem 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 0.375rem 0.375rem;
}

/* Logo Preview Modal */
.logo-preview {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
}

.logo-preview .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logo-preview .card-header {
    background: white;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.logo-preview img {
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

/* Configuration Modal */
.config-section {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
}

.config-section h6 {
    color: #0d6efd;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #0d6efd;
}

.config-section:last-child {
    margin-bottom: 0;
}

/* Preset Items Modal */
.preset-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
    padding: 0.5rem;
}

.preset-item-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preset-item-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
    transform: translateY(-2px);
}

.preset-item-card.selected {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.preset-item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.preset-item-category {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.preset-item-price {
    font-weight: bold;
    color: #198754;
    font-family: 'Courier New', monospace;
}

/* Add Item Modal */
.add-item-form .form-group {
    margin-bottom: 1rem;
}

.add-item-form .form-label {
    font-weight: 600;
    color: #495057;
}

.add-item-form .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.price-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.price-preview {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.75rem;
    margin-top: 1rem;
}

.price-preview-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.price-preview-amount {
    font-size: 1.25rem;
    font-weight: bold;
    color: #198754;
    font-family: 'Courier New', monospace;
}

/* Loading States */
.modal-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
}

.modal-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Error States */
.modal-error {
    text-align: center;
    padding: 2rem;
    color: #dc3545;
}

.modal-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Responsive Modal Adjustments */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .preset-items-grid {
        grid-template-columns: 1fr;
    }
    
    .price-inputs {
        grid-template-columns: 1fr;
    }
    
    .toast-message {
        left: 10px;
        right: 10px;
        min-width: auto;
    }
}

/* Confirmation Dialogs */
.confirmation-dialog .modal-body {
    text-align: center;
    padding: 2rem;
}

.confirmation-dialog .modal-body i {
    font-size: 3rem;
    color: #ffc107;
    margin-bottom: 1rem;
}

.confirmation-dialog .modal-body h5 {
    color: #333;
    margin-bottom: 1rem;
}

.confirmation-dialog .modal-body p {
    color: #6c757d;
    margin-bottom: 0;
}

/* Success/Error Icons in Modals */
.modal-icon-success {
    color: #198754;
    font-size: 1.25rem;
}

.modal-icon-error {
    color: #dc3545;
    font-size: 1.25rem;
}

.modal-icon-warning {
    color: #ffc107;
    font-size: 1.25rem;
}

.modal-icon-info {
    color: #0dcaf0;
    font-size: 1.25rem;
}

/* Golden Select Preset Button */
.btn-select-preset {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
    border: 2px solid #DAA520;
    color: #8B4513;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.3);
    box-shadow: 0 4px 8px rgba(255,215,0,0.3), inset 0 1px 0 rgba(255,255,255,0.4);
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 0.5rem 1rem;
}

.btn-select-preset:hover {
    background: linear-gradient(135deg, #FFED4E 0%, #FFB347 50%, #FF7F50 100%);
    border-color: #B8860B;
    color: #654321;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255,215,0,0.4), inset 0 1px 0 rgba(255,255,255,0.5);
}

.btn-select-preset:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255,215,0,0.3), inset 0 1px 0 rgba(255,255,255,0.3);
}

.btn-select-preset:focus {
    box-shadow: 0 0 0 0.2rem rgba(255,215,0,0.5);
}

.btn-select-preset i {
    margin-right: 0.5rem;
}
