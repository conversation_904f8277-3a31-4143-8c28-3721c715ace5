/* KMS PC Receipt Maker - 主樣式文件 */

/* 全局樣式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.05"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: -1;
}

/* 導航欄樣式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* 卡片樣式 */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    padding: 1.25rem 1.5rem;
    border-radius: 15px 15px 0 0 !important;
}

.card-header h5, .card-header h6 {
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* 表單樣式 */
.form-control, .form-select {
    border-radius: 15px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

/* 按鈕樣式 */
.btn {
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    padding: 0.75rem 1.5rem;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    box-shadow: 0 4px 15px rgba(255, 65, 108, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
    border-radius: 30px;
}

/* 區域切換 */
.section {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 收據項目樣式 */
.receipt-item {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.receipt-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.125rem 0.25rem rgba(13, 110, 253, 0.15);
}

.receipt-item .remove-item {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.receipt-item .remove-item:hover {
    background: #bb2d3b;
    transform: scale(1.1);
}

/* 預覽區域 */
.receipt-preview {
    background-color: white;
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 2rem;
    min-height: 400px;
    transition: var(--transition);
}

.receipt-preview.has-content {
    border-style: solid;
    border-color: var(--primary-color);
}

/* 歷史記錄樣式 */
.receipt-history-item {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.receipt-history-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.125rem 0.25rem rgba(13, 110, 253, 0.15);
    transform: translateY(-1px);
}

.receipt-number {
    font-weight: 600;
    color: var(--primary-color);
}

.receipt-amount {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--success-color);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        margin-bottom: 0.5rem;
    }

    .d-flex.gap-2.flex-wrap .btn {
        flex: 1 1 auto;
        min-width: 120px;
    }

    .receipt-item {
        padding: 0.75rem;
    }

    .navbar-brand {
        font-size: 1rem;
    }

    .form-group.mb-0 {
        margin-bottom: 0.5rem !important;
    }

    .d-flex.gap-2.align-items-center.flex-wrap {
        flex-direction: column;
        align-items: stretch !important;
    }

    .d-flex.gap-2.align-items-center.flex-wrap > * {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .receipt-preview {
        min-height: 300px;
        padding: 1rem;
    }
}

/* 載入動畫 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 美化增強樣式 */
.receipt-item-row {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.receipt-item-row:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
}

.modal-header {
    border-radius: 20px 20px 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: none;
    padding: 1.5rem;
    border-radius: 0 0 20px 20px;
}

.navbar {
    backdrop-filter: blur(20px);
    background: rgba(13, 110, 253, 0.9) !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}





/* 預設項目樣式 */
.preset-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    border-radius: 15px;
    transition: all 0.3s ease;
    height: 60px;
    display: flex;
    align-items: center;
}

.preset-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

/* 預設項目列表容器 */
#presetList {
    padding: 1rem;
}

/* 預設項目按鈕組 */
.preset-item .btn-group {
    flex-wrap: wrap;
    gap: 0.25rem;
}

.preset-item .btn-group .btn {
    margin: 0.125rem;
}

/* 拖拽排序樣式 */
.order-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.order-input {
    width: 50px;
    text-align: center;
    font-size: 0.8rem;
}

.drag-handle {
    cursor: grab;
    color: #6c757d;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.drag-handle:hover {
    color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
}

.drag-handle:active {
    cursor: grabbing;
}

.preset-item[draggable="true"] {
    cursor: move;
}

.preset-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.preset-item.drag-over {
    border-top: 3px solid #667eea !important;
    transform: translateY(-2px);
}

/* 配置項目樣式 */
.configuration-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.configuration-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

/* Logo 預覽樣式 */
.logo-preview {
    animation: slideInUp 0.3s ease-out;
}

.logo-preview .card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.logo-preview .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

#logoPreviewImage {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

#logoPreviewImage:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

/* 超小按鈕樣式 */
.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.2rem;
    min-width: 28px;
    height: 24px;
}

.btn-group-vertical .btn-xs {
    margin-bottom: 2px;
}

.btn-group-vertical .btn-xs:last-child {
    margin-bottom: 0;
}

/* Select Preset 模態框尺寸 */
#presetModal .modal-dialog {
    min-width: 75%;
    min-height: 75%;
}

#presetModal .modal-content {
    min-height: 75vh;
    background: linear-gradient(135deg, #20b2aa 0%, #008b8b 100%); /* 藍綠色背景 */
}

/* Preset 列表背景 */
#presetList {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%); /* 金黃色背景 */
    border-radius: 10px;
    padding: 1rem;
}

/* 成功/錯誤訊息 */
.alert {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d1edff 0%, #a7d8f0 100%);
    color: #0c5460;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
    color: #721c24;
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

/* 自定義滾動條 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 項目列表優化樣式 */
.item-row {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid #e9ecef;
    border-radius: 15px;
    transition: all 0.3s ease;
    padding: 1rem;
    margin-bottom: 1rem;
}

.item-row:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.item-row .btn-group {
    flex-wrap: nowrap;
}

.item-row .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 8px;
    margin: 0 2px;
}

/* 總計顯示區域優化 */
#totalsDisplay .card {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
}

#totalsDisplay .card-body {
    padding: 1.5rem;
}

#totalsDisplay .row {
    margin-bottom: 0.5rem;
    align-items: center;
}

#totalsDisplay hr {
    margin: 1rem 0;
    border-top: 2px solid #667eea;
}

/* 模態框優化 */
.modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.98);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 20px 20px 0 0;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0 0 20px 20px;
    padding: 1.5rem;
}

/* 表單控件優化 */
.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.9);
}

/* 按鈕組優化 */
.btn-group .btn {
    margin: 0 1px;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 預覽區域優化 */
.receipt-preview {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 15px;
    padding: 2rem;
    min-height: 400px;
    transition: all 0.3s ease;
}

.receipt-preview.has-content {
    border-style: solid;
    border-color: #667eea;
    background: white;
}

/* 搜索和篩選區域優化 */
.d-flex.gap-2.align-items-center.flex-wrap {
    gap: 0.5rem !important;
}

.d-flex.gap-2.align-items-center.flex-wrap > * {
    margin-bottom: 0.5rem;
}

/* 卡片標題優化 */
.card-title {
    font-weight: 600;
    color: #333;
}

.card-header h5, .card-header h6 {
    margin: 0;
    font-weight: 600;
    color: white;
}

/* 徽章優化 */
.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
}

/* 動畫優化 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.container {
    animation: slideInUp 0.6s ease-out;
}

/* 載入狀態 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 錯誤和成功訊息優化 */
.alert {
    border-radius: 15px;
    border: none;
    backdrop-filter: blur(10px);
    animation: slideInDown 0.3s ease-out;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-success {
    background: linear-gradient(135deg, #d1edff 0%, #a7d8f0 100%);
    color: #0c5460;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}