/**
 * Receipt Generator
 * Handles receipt generation, HTML creation, and preview functionality
 */

class ReceiptGenerator {
    constructor() {
        this.defaultPaymentMethod = 'cash';
    }

    /**
     * Generate Receipt
     */
    generateReceipt() {
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];

        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('Please add items to the receipt first', 'warning');
            }
            return;
        }

        // Auto-generate customer info if empty and fill the form fields
        let customerName = document.getElementById('customerName')?.value.trim();
        if (!customerName) {
            customerName = this.generateCustomerName();
            if (document.getElementById('customerName')) {
                document.getElementById('customerName').value = customerName;
            }
        }

        let customerPhone = document.getElementById('customerPhone')?.value.trim();
        if (!customerPhone) {
            customerPhone = this.generateCustomerPhone();
            if (document.getElementById('customerPhone')) {
                document.getElementById('customerPhone').value = customerPhone;
            }
        }

        let customerEmail = document.getElementById('customerEmail')?.value.trim();
        if (!customerEmail) {
            customerEmail = this.generateCustomerEmail();
            if (document.getElementById('customerEmail')) {
                document.getElementById('customerEmail').value = customerEmail;
            }
        }

        // Collect receipt data
        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod, // Use default since form field was removed
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                hidePrice: item.hidePrice || false
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || '',
            receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
            // Removed receiptDate - no longer needed
        };

        // Generate receipt HTML and display in preview area
        const receiptHtml = this.generateReceiptHtml(receiptData);
        this.displayReceiptPreview(receiptHtml);

        // Show action buttons
        const previewActions = document.getElementById('previewActions');
        if (previewActions) {
            previewActions.classList.remove('d-none');
        }

        if (typeof UIManager !== 'undefined') {
            UIManager.showMessage('Receipt generated successfully!', 'success');
        }
    }

    /**
     * Generate Receipt HTML
     */
    generateReceiptHtml(data) {
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        };

        return `
            <div class="receipt-container">
                <div class="receipt-inner">
                    ${window.currentLogo && window.currentLogo.src ? `
                    <div class="receipt-logo">
                        <img src="${window.currentLogo.src}" alt="Company Logo">
                    </div>
                    ` : ''}

                    <div class="receipt-header">
                        <div class="receipt-title">KelvinKMS</div>
                        <div class="receipt-company-info">
                            KelvinKMS.com<br>
                            626-464-8036<br>
                            <EMAIL>
                        </div>
                    </div>

                    <div class="receipt-info">
                        <div>
                            <div class="receipt-number">
                                Receipt Number: ${data.receiptNumber}
                            </div>
                        </div>
                    </div>

                    <div class="customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value handwrite-space">&nbsp;</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value handwrite-space">&nbsp;</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Email:</span>
                            <span class="customer-field-value handwrite-space">&nbsp;</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Address:</span>
                            <span class="customer-field-value handwrite-space">&nbsp;</span>
                        </div>
                    </div>

                    <div class="receipt-items">
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-left">Item</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Price</th>
                                    <th class="text-right">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.items.map(item => `
                                    <tr>
                                        <td class="text-left">
                                            <div class="item-name">${item.name}</div>
                                            ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                                        </td>
                                        <td class="text-center">${item.quantity}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(item.unitPrice)}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(item.totalPrice)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="receipt-totals">
                        <table class="totals-table">
                            <tr>
                                <td class="label">Subtotal:</td>
                                <td class="amount">${formatCurrency(data.totals.subtotal)}</td>
                            </tr>
                            ${data.totals.discount > 0 ? `
                            <tr>
                                <td class="label">Discount:</td>
                                <td class="amount">-${formatCurrency(data.totals.discount)}</td>
                            </tr>
                            ` : ''}
                            ${data.totals.tax > 0 ? `
                            <tr>
                                <td class="label">Tax:</td>
                                <td class="amount">${formatCurrency(data.totals.tax)}</td>
                            </tr>
                            ` : ''}
                            <tr class="total-row">
                                <td class="label">Total:</td>
                                <td class="amount">${formatCurrency(data.totals.total)}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="payment-method">
                        <div class="payment-options">
                            ${['Cash','Venmo','Zelle','Square','Stripe'].map(pm => {
                                return `
                                <div class="payment-option-button">
                                    <div class="payment-checkbox"></div>
                                    <span class="payment-label">${pm}</span>
                                </div>
                                `;
                            }).join('')}
                        </div>
                    </div>

                    ${data.notes ? `
                    <div class="receipt-notes">
                        <h6>Notes</h6>
                        <p>${data.notes}</p>
                    </div>
                    ` : ''}

                    <div class="signature-section">
                        <div class="signature-labels-row">
                            <div class="signature-label-item">
                                <span class="signature-label">Seller Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Buyer Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Signature Date:</span>
                            </div>
                        </div>
                        <div class="signature-lines-area">
                            <div class="signature-line-space"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Display Receipt Preview
     */
    displayReceiptPreview(html) {
        const previewElement = document.getElementById('receiptPreview');
        if (previewElement) {
            previewElement.innerHTML = html;
            previewElement.classList.add('has-content');
        }
    }

    /**
     * Update Receipt Preview
     */
    updateReceiptPreview() {
        // This function can be called to refresh the preview
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
        if (receiptItems.length > 0) {
            // Only update if there are items
            this.generateReceipt();
        } else {
            // Clear preview if no items
            const previewElement = document.getElementById('receiptPreview');
            if (previewElement) {
                previewElement.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here with beautiful certificate border</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Calculate Totals (fallback method)
     */
    calculateTotals(items) {
        const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value) || 0;
        const taxRate = parseFloat(document.getElementById('taxRate')?.value) || 0;

        const discountedSubtotal = Math.max(0, subtotal - discountAmount);
        const taxAmount = discountedSubtotal * (taxRate / 100);
        const total = discountedSubtotal + taxAmount;

        return {
            subtotal: subtotal,
            discount: discountAmount,
            tax: taxAmount,
            total: total
        };
    }

    /**
     * Generate Customer Name
     */
    generateCustomerName() {
        const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily', 'James', 'Jessica'];
        const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];

        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

        return `${firstName} ${lastName}`;
    }

    /**
     * Generate Customer Phone
     */
    generateCustomerPhone() {
        const areaCode = Math.floor(Math.random() * 900) + 100;
        const exchange = Math.floor(Math.random() * 900) + 100;
        const number = Math.floor(Math.random() * 9000) + 1000;

        return `(${areaCode}) ${exchange}-${number}`;
    }

    /**
     * Generate Customer Email
     */
    generateCustomerEmail() {
        const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        const domain = domains[Math.floor(Math.random() * domains.length)];
        const username = 'customer' + Math.floor(Math.random() * 10000);

        return `${username}@${domain}`;
    }

    /**
     * Print Receipt
     */
    printReceipt() {
        const receiptContent = document.getElementById('receiptPreview')?.innerHTML;
        if (!receiptContent || receiptContent.includes('Receipt preview will be shown here')) {
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('Please generate a receipt first', 'warning');
            }
            return;
        }

        const printWindow = window.open('', '_blank');
        const currentHost = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');

        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Receipt Print</title>
                <link href="${currentHost}css/receipt-preview.css" rel="stylesheet">
                <style>
                    @page {
                        size: letter;
                        margin: 0.5in;
                    }

                    body {
                        margin: 0;
                        padding: 0;
                        background: white !important;
                        font-family: 'Courier New', monospace;
                        font-size: 12pt;
                        line-height: 1.4;
                        width: 7.5in;
                    }

                    /* Ensure exact same styling as preview */
                    .receipt-container {
                        background: white !important;
                        padding: 2rem 2.5rem;
                        border-radius: 25px;
                        box-shadow:
                            0 0 0 3px #D4AF37,
                            0 0 0 6px white,
                            0 0 0 9px #B8860B,
                            0 0 0 12px white,
                            0 0 0 15px #DAA520;
                        max-width: 8.5in;
                        margin: 0 auto;
                        font-family: 'Courier New', monospace;
                        line-height: 1.4;
                        font-size: 12pt;
                        position: relative;
                        page-break-inside: avoid;
                    }

                    /* Multi-page support */
                    .receipt-table {
                        page-break-inside: auto;
                    }

                    .receipt-table tr {
                        page-break-inside: avoid;
                        page-break-after: auto;
                    }

                    .receipt-table thead {
                        display: table-header-group;
                    }

                    /* Force page break when content is too long */
                    .page-break-before {
                        page-break-before: always;
                    }

                    .no-print {
                        display: none !important;
                    }

                    /* Ensure all elements match preview exactly */
                    * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }
                </style>
            </head>
            <body>
                ${receiptContent}
                <script>
                    window.onload = function() {
                        // Add page breaks if content is too long
                        const container = document.querySelector('.receipt-container');
                        if (container) {
                            const containerHeight = container.offsetHeight;
                            const pageHeight = 11 * 72 - 72; // Letter height minus margins in points

                            if (containerHeight > pageHeight) {
                                // Find a good place to break
                                const tables = container.querySelectorAll('.receipt-table tbody tr');
                                let currentHeight = 0;

                                tables.forEach((row, index) => {
                                    currentHeight += row.offsetHeight;
                                    if (currentHeight > pageHeight * 0.8 && index < tables.length - 1) {
                                        row.classList.add('page-break-before');
                                        currentHeight = 0;
                                    }
                                });
                            }
                        }

                        setTimeout(function() {
                            window.print();
                            window.close();
                        }, 1000);
                    };
                </script>
            </body>
            </html>
        `);
        printWindow.document.close();
    }

    /**
     * Save Receipt
     */
    async saveReceipt() {
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];

        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('Please add items to the receipt first', 'warning');
            }
            return;
        }

        // Auto-generate customer info if empty and fill the form fields
        let customerName = document.getElementById('customerName')?.value.trim();
        if (!customerName) {
            customerName = this.generateCustomerName();
            if (document.getElementById('customerName')) {
                document.getElementById('customerName').value = customerName;
            }
        }

        let customerPhone = document.getElementById('customerPhone')?.value.trim();
        if (!customerPhone) {
            customerPhone = this.generateCustomerPhone();
            if (document.getElementById('customerPhone')) {
                document.getElementById('customerPhone').value = customerPhone;
            }
        }

        let customerEmail = document.getElementById('customerEmail')?.value.trim();
        if (!customerEmail) {
            customerEmail = this.generateCustomerEmail();
            if (document.getElementById('customerEmail')) {
                document.getElementById('customerEmail').value = customerEmail;
            }
        }

        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod,
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || ''
        };

        try {
            const response = await fetch('php/save_receipt.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(receiptData)
            });

            const result = await response.json();

            if (result.success) {
                if (typeof UIManager !== 'undefined') {
                    UIManager.showMessage('Receipt saved successfully!', 'success');
                }
            } else {
                if (typeof UIManager !== 'undefined') {
                    UIManager.showMessage('Failed to save receipt: ' + result.message, 'error');
                }
            }
        } catch (error) {
            console.error('Error saving receipt:', error);
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('Error occurred while saving receipt', 'error');
            }
        }
    }
}

// Create global instance
window.ReceiptGenerator = new ReceiptGenerator();

// Export functions for backward compatibility
window.generateReceipt = () => window.ReceiptGenerator.generateReceipt();
window.generateReceiptHtml = (data) => window.ReceiptGenerator.generateReceiptHtml(data);
window.displayReceiptPreview = (html) => window.ReceiptGenerator.displayReceiptPreview(html);
window.updateReceiptPreview = () => window.ReceiptGenerator.updateReceiptPreview();
window.printReceipt = () => window.ReceiptGenerator.printReceipt();
window.saveReceipt = () => window.ReceiptGenerator.saveReceipt();
