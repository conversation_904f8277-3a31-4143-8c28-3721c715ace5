<?php
/**
 * KMS PC Receipt Maker 安裝腳本
 */

echo "<h1>KMS PC Receipt Maker 安裝</h1>";

// 檢查PHP版本
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    echo "<p style='color: red;'>錯誤: 需要PHP 7.4或更高版本，當前版本: " . PHP_VERSION . "</p>";
    exit;
}

echo "<p style='color: green;'>✓ PHP版本檢查通過: " . PHP_VERSION . "</p>";

// 檢查必需的擴展
$requiredExtensions = ['json', 'mbstring'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    echo "<p style='color: red;'>錯誤: 缺少必需的PHP擴展: " . implode(', ', $missingExtensions) . "</p>";
    exit;
}

echo "<p style='color: green;'>✓ PHP擴展檢查通過</p>";

// 創建數據目錄
$dataDir = __DIR__ . '/data';
if (!is_dir($dataDir)) {
    if (mkdir($dataDir, 0755, true)) {
        echo "<p style='color: green;'>✓ 創建數據目錄成功</p>";
    } else {
        echo "<p style='color: red;'>錯誤: 無法創建數據目錄</p>";
        exit;
    }
} else {
    echo "<p style='color: green;'>✓ 數據目錄已存在</p>";
}

// 檢查目錄權限
if (!is_writable($dataDir)) {
    echo "<p style='color: red;'>錯誤: 數據目錄不可寫，請設置正確的權限</p>";
    exit;
}

echo "<p style='color: green;'>✓ 數據目錄權限檢查通過</p>";

// 初始化數據文件
require_once 'php/Database.php';

try {
    $db = new Database();
    echo "<p style='color: green;'>✓ 數據庫連接成功</p>";
    
    // 檢查電腦零件數據
    $pcPartsFile = $dataDir . '/pc_parts.json';
    if (file_exists($pcPartsFile)) {
        $pcParts = json_decode(file_get_contents($pcPartsFile), true);
        echo "<p style='color: green;'>✓ 電腦零件數據已載入 (" . count($pcParts) . " 項)</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>錯誤: " . $e->getMessage() . "</p>";
    exit;
}

// 測試API
echo "<h2>API測試</h2>";

// 測試電腦零件API
$testUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/php/get_pc_parts.php';
$context = stream_context_create([
    'http' => [
        'timeout' => 5
    ]
]);

$response = @file_get_contents($testUrl, false, $context);
if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<p style='color: green;'>✓ 電腦零件API測試通過</p>";
    } else {
        echo "<p style='color: orange;'>警告: 電腦零件API響應異常</p>";
    }
} else {
    echo "<p style='color: orange;'>警告: 無法測試電腦零件API（可能是網絡問題）</p>";
}

echo "<h2>安裝完成</h2>";
echo "<p style='color: green; font-weight: bold;'>✓ KMS PC Receipt Maker 安裝成功！</p>";
echo "<p><a href='index.html' style='background: #0d6efd; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>開始使用</a></p>";

echo "<h3>系統信息</h3>";
echo "<ul>";
echo "<li>PHP版本: " . PHP_VERSION . "</li>";
echo "<li>數據存儲: 文件系統 (data/)</li>";
echo "<li>支持語言: 繁體中文、English</li>";
echo "<li>瀏覽器要求: 現代瀏覽器 (Chrome, Firefox, Safari, Edge)</li>";
echo "</ul>";

echo "<h3>下一步</h3>";
echo "<ol>";
echo "<li>訪問 <a href='index.html'>主頁面</a> 開始使用</li>";
echo "<li>創建第一個收據測試功能</li>";
echo "<li>如有問題，請檢查 README.md 文件</li>";
echo "</ol>";
?>
