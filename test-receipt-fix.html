<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS Receipt Maker - Receipt.js Fix Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>KMS Receipt Maker - Receipt.js Fix Test</h1>
        
        <!-- Error Status Display -->
        <div class="card mb-4">
            <div class="card-header" id="errorHeader">
                <h5>JavaScript Error Monitor</h5>
            </div>
            <div class="card-body">
                <div id="errorDisplay">
                    <p class="text-success">✅ No JavaScript errors detected</p>
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Receipt Generation Tests</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Basic Tests</h6>
                        <button class="btn btn-primary btn-sm me-2 mb-2" onclick="testAddItem()">Add Test Item</button>
                        <button class="btn btn-success btn-sm me-2 mb-2" onclick="testGenerateReceipt()">Generate Receipt</button>
                        <button class="btn btn-info btn-sm me-2 mb-2" onclick="testEmptyCustomer()">Test Empty Customer</button>
                    </div>
                    <div class="col-md-6">
                        <h6>Advanced Tests</h6>
                        <button class="btn btn-warning btn-sm me-2 mb-2" onclick="testWithLogo()">Test With Logo</button>
                        <button class="btn btn-secondary btn-sm me-2 mb-2" onclick="testPrintFunction()">Test Print</button>
                        <button class="btn btn-danger btn-sm me-2 mb-2" onclick="clearTestResults()">Clear Results</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Customer Information (All Optional)</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customerName" class="form-label">Customer Name</label>
                        <input type="text" class="form-control" id="customerName" placeholder="Optional">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerPhone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="customerPhone" placeholder="Optional">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="customerEmail" placeholder="Optional">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerAddress" class="form-label">Address</label>
                        <textarea class="form-control" id="customerAddress" rows="2" placeholder="Optional"></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="receiptNumber" class="form-label">Receipt Number</label>
                        <input type="text" class="form-control" id="receiptNumber" value="KMS-UltraVIP-0000001">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="2" placeholder="Additional notes"></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="discountAmount" class="form-label">Discount Amount</label>
                        <input type="number" class="form-control discount-input" id="discountAmount" min="0" step="0.01" value="0">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="taxRate" class="form-label">Tax Rate (%)</label>
                        <input type="number" class="form-control tax-input" id="taxRate" min="0" max="100" step="0.1" value="0">
                    </div>
                </div>
            </div>
        </div>

        <!-- Receipt Items -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Receipt Items
                    </h5>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllItems()">
                        <i class="fas fa-trash-alt me-1"></i>
                        Clear All
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="receiptItemsList" class="receipt-items-container">
                    <div class="receipt-items-empty">
                        <i class="fas fa-inbox"></i>
                        <p>No items added yet</p>
                    </div>
                </div>
                <div id="receiptTotals" class="mt-3"></div>
            </div>
        </div>

        <!-- Receipt Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Receipt Preview
                </h5>
            </div>
            <div class="card-body">
                <div id="receiptPreview">
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here</p>
                    </div>
                </div>
                <div id="previewActions" class="d-none mt-3">
                    <button type="button" class="btn btn-success btn-sm" onclick="saveReceipt()">
                        <i class="fas fa-save me-1"></i>
                        Save Receipt
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="printReceipt()">
                        <i class="fas fa-print me-1"></i>
                        Print Receipt
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card">
            <div class="card-header">
                <h5>Test Results</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">Test results will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Error Monitoring -->
    <script>
        let errorCount = 0;
        const originalConsoleError = console.error;
        
        window.addEventListener('error', function(e) {
            errorCount++;
            const errorDisplay = document.getElementById('errorDisplay');
            const errorHeader = document.getElementById('errorHeader');
            errorDisplay.innerHTML = `<p class="text-danger">❌ JavaScript Error ${errorCount}: ${e.message}<br><small>File: ${e.filename}:${e.lineno}</small></p>`;
            errorHeader.className = 'card-header bg-danger text-white';
        });
        
        console.error = function(...args) {
            errorCount++;
            const errorDisplay = document.getElementById('errorDisplay');
            const errorHeader = document.getElementById('errorHeader');
            if (errorDisplay) {
                errorDisplay.innerHTML = `<p class="text-danger">❌ Console Error ${errorCount}: ${args.join(' ')}</p>`;
                errorHeader.className = 'card-header bg-danger text-white';
            }
            originalConsoleError.apply(console, args);
        };
    </script>
    
    <!-- Custom JS - Load in order -->
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    
    <!-- Core modules (load first) -->
    <script src="js/core/app-initializer.js"></script>
    
    <!-- Feature modules -->
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    
    <!-- Main coordinator -->
    <script src="js/main-new.js"></script>
    
    <!-- Legacy modules that depend on new system -->
    <script src="js/preset-manager.js"></script>
    <script src="js/config-manager.js"></script>
    <script src="js/receipt.js"></script>

    <!-- Test Functions -->
    <script>
        let testCounter = 0;

        function logTest(message, success = true) {
            testCounter++;
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = success ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>';
            resultsDiv.innerHTML += `<div class="mb-1">${statusIcon} [${timestamp}] Test ${testCounter}: ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearTestResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-muted">Test results will appear here...</p>';
            testCounter = 0;
        }

        function testAddItem() {
            try {
                const testItem = {
                    id: Date.now(),
                    name: 'Test RTX 4090 Graphics Card',
                    category: 'GPU',
                    description: 'High-end gaming graphics card',
                    original_price: 1999.99,
                    special_price: 1599.99,
                    default_price: 1599.99
                };
                addItemToReceipt(testItem);
                logTest('Test item added successfully');
            } catch (error) {
                logTest(`Failed to add test item: ${error.message}`, false);
            }
        }

        function testGenerateReceipt() {
            try {
                generateReceipt();
                logTest('Receipt generated successfully without errors');
            } catch (error) {
                logTest(`Receipt generation failed: ${error.message}`, false);
            }
        }

        function testEmptyCustomer() {
            try {
                // Clear all customer fields
                document.getElementById('customerName').value = '';
                document.getElementById('customerPhone').value = '';
                document.getElementById('customerEmail').value = '';
                document.getElementById('customerAddress').value = '';
                
                generateReceipt();
                logTest('Receipt generated with empty customer fields (handwritten mode)');
            } catch (error) {
                logTest(`Empty customer test failed: ${error.message}`, false);
            }
        }

        function testWithLogo() {
            try {
                // Create a test logo
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                
                ctx.fillStyle = '#D4AF37';
                ctx.fillRect(0, 0, 200, 100);
                ctx.fillStyle = '#333';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('KelvinKMS', 100, 35);
                ctx.font = '16px Arial';
                ctx.fillText('Test Logo', 100, 65);
                
                window.currentLogo = {
                    src: canvas.toDataURL('image/png'),
                    name: 'test-logo.png',
                    size: 5000,
                    type: 'image/png',
                    width: 200,
                    height: 100
                };
                
                generateReceipt();
                logTest('Receipt generated with logo successfully');
            } catch (error) {
                logTest(`Logo test failed: ${error.message}`, false);
            }
        }

        function testPrintFunction() {
            try {
                const receiptContent = document.getElementById('receiptPreview')?.innerHTML;
                if (receiptContent && !receiptContent.includes('Receipt preview will be shown here')) {
                    logTest('Print function ready - receipt content available');
                } else {
                    logTest('Generate a receipt first before testing print', false);
                }
            } catch (error) {
                logTest(`Print test failed: ${error.message}`, false);
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Receipt.js fix test page loaded');
            
            // Test basic functionality after modules load
            setTimeout(() => {
                logTest(`All modules loaded successfully`);
                
                // Check if receiptDate element exists (should not)
                const dateField = document.getElementById('receiptDate');
                if (!dateField) {
                    logTest('✅ receiptDate element correctly removed from DOM');
                } else {
                    logTest('❌ receiptDate element still exists in DOM', false);
                }
                
                // Test basic receipt generation
                testAddItem();
                setTimeout(() => {
                    testGenerateReceipt();
                }, 500);
                
                if (errorCount === 0) {
                    logTest('✅ No JavaScript errors detected during initialization');
                } else {
                    logTest(`❌ ${errorCount} JavaScript errors detected during initialization`, false);
                }
            }, 1000);
        });
    </script>
</body>
</html>
