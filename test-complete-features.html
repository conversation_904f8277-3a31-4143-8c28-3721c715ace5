<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS Receipt Maker - Complete Features Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>KMS Receipt Maker - Complete Features Test</h1>
        
        <!-- Feature Checklist -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5>Feature Verification Checklist</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Required Features:</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="check1">
                            <label class="form-check-label" for="check1">Customer name not required</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="check2">
                            <label class="form-check-label" for="check2">Logo appears in receipt preview</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="check3">
                            <label class="form-check-label" for="check3">Date Issued completely removed</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="check4">
                            <label class="form-check-label" for="check4">Customer info shows blank lines</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Design Features:</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="check5">
                            <label class="form-check-label" for="check5">Payment method buttons (Cash, Venmo, Zelle, Square, Stripe)</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="check6">
                            <label class="form-check-label" for="check6">Signatures on same line with space below</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="check7">
                            <label class="form-check-label" for="check7">Certificate-style border around receipt</label>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-success" onclick="runAllTests()">Run All Tests</button>
                    <button class="btn btn-info" onclick="generateTestReceipt()">Generate Test Receipt</button>
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Test Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-primary btn-sm me-2 mb-2" onclick="testAddItem()">Add Test Item</button>
                        <button class="btn btn-warning btn-sm me-2 mb-2" onclick="testUploadLogo()">Upload Test Logo</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-success btn-sm me-2 mb-2" onclick="testWithoutCustomer()">Test Without Customer</button>
                        <button class="btn btn-info btn-sm me-2 mb-2" onclick="testWithCustomer()">Test With Customer</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-secondary btn-sm me-2 mb-2" onclick="clearAll()">Clear All</button>
                        <button class="btn btn-danger btn-sm me-2 mb-2" onclick="clearTestResults()">Clear Results</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information (All Optional) -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Customer Information (All Optional - Leave Empty for Handwritten Receipt)</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customerName" class="form-label">Customer Name (Optional)</label>
                        <input type="text" class="form-control" id="customerName" placeholder="Leave empty for handwritten receipt">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerPhone" class="form-label">Phone (Optional)</label>
                        <input type="tel" class="form-control" id="customerPhone" placeholder="Optional">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerEmail" class="form-label">Email (Optional)</label>
                        <input type="email" class="form-control" id="customerEmail" placeholder="Optional">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerAddress" class="form-label">Address (Optional)</label>
                        <textarea class="form-control" id="customerAddress" rows="2" placeholder="Optional"></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="receiptNumber" class="form-label">Receipt Number</label>
                        <input type="text" class="form-control" id="receiptNumber" value="KMS-UltraVIP-0000001">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="2" placeholder="Additional notes"></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="discountAmount" class="form-label">Discount Amount</label>
                        <input type="number" class="form-control discount-input" id="discountAmount" min="0" step="0.01" value="0">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="taxRate" class="form-label">Tax Rate (%)</label>
                        <input type="number" class="form-control tax-input" id="taxRate" min="0" max="100" step="0.1" value="0">
                    </div>
                </div>
            </div>
        </div>

        <!-- Logo Upload -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Company Logo</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="logoUpload" class="form-label">Upload Logo</label>
                    <input type="file" class="form-control" id="logoUpload" accept="image/*">
                </div>
                <div id="logoPreview" style="display: none;">
                    <div class="card">
                        <div class="card-header">Logo Preview</div>
                        <div class="card-body text-center">
                            <img id="logoPreviewImage" src="" alt="Logo Preview" style="max-width: 200px; max-height: 100px;">
                            <div class="mt-2">
                                <small id="logoFileName"></small><br>
                                <small id="logoFileType"></small> | <small id="logoFileSize"></small><br>
                                <small id="logoImageDimensions"></small>
                            </div>
                            <button type="button" class="btn btn-danger btn-sm mt-2" onclick="removeLogo()">Remove Logo</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Receipt Items -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Receipt Items
                    </h5>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllItems()">
                        <i class="fas fa-trash-alt me-1"></i>
                        Clear All
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="receiptItemsList" class="receipt-items-container">
                    <div class="receipt-items-empty">
                        <i class="fas fa-inbox"></i>
                        <p>No items added yet</p>
                    </div>
                </div>
                <div id="receiptTotals" class="mt-3"></div>
            </div>
        </div>

        <!-- Receipt Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Receipt Preview with Certificate Border
                </h5>
            </div>
            <div class="card-body">
                <div id="receiptPreview">
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here with beautiful certificate border</p>
                        <p><small>Features: Handwritten customer fields, premium payment buttons, signature area, certificate border</small></p>
                    </div>
                </div>
                <div id="previewActions" class="d-none mt-3">
                    <button type="button" class="btn btn-success btn-sm" onclick="saveReceipt()">
                        <i class="fas fa-save me-1"></i>
                        Save Receipt
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="printReceipt()">
                        <i class="fas fa-print me-1"></i>
                        Print Receipt
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card">
            <div class="card-header">
                <h5>Test Results</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">Test results will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS - Load in order -->
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    
    <!-- Core modules (load first) -->
    <script src="js/core/app-initializer.js"></script>
    
    <!-- Feature modules -->
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    
    <!-- Main coordinator -->
    <script src="js/main-new.js"></script>
    
    <!-- Legacy modules that depend on new system -->
    <script src="js/preset-manager.js"></script>
    <script src="js/config-manager.js"></script>
    <script src="js/receipt.js"></script>

    <!-- Test Functions -->
    <script>
        let testCounter = 0;

        function logTest(message, success = true) {
            testCounter++;
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = success ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>';
            resultsDiv.innerHTML += `<div class="mb-1">${statusIcon} [${timestamp}] Test ${testCounter}: ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearTestResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-muted">Test results will appear here...</p>';
            testCounter = 0;
        }

        function testAddItem() {
            try {
                const testItem = {
                    id: Date.now(),
                    name: 'Test RTX 4090 Graphics Card',
                    category: 'GPU',
                    description: 'High-end gaming graphics card',
                    original_price: 1999.99,
                    special_price: 1599.99,
                    default_price: 1599.99
                };
                addItemToReceipt(testItem);
                logTest('Test item added successfully');
            } catch (error) {
                logTest(`Failed to add test item: ${error.message}`, false);
            }
        }

        function testUploadLogo() {
            try {
                // Create a test logo data URL
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                
                // Draw a simple test logo
                ctx.fillStyle = '#D4AF37';
                ctx.fillRect(0, 0, 200, 100);
                ctx.fillStyle = '#333';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('KelvinKMS', 100, 35);
                ctx.font = '16px Arial';
                ctx.fillText('Test Logo', 100, 65);
                
                const dataURL = canvas.toDataURL('image/png');
                
                window.currentLogo = {
                    src: dataURL,
                    name: 'test-logo.png',
                    size: 5000,
                    type: 'image/png',
                    width: 200,
                    height: 100
                };
                
                // Show preview
                const previewContainer = document.getElementById('logoPreview');
                const previewImage = document.getElementById('logoPreviewImage');
                const fileName = document.getElementById('logoFileName');
                const fileType = document.getElementById('logoFileType');
                const fileSize = document.getElementById('logoFileSize');
                const imageDimensions = document.getElementById('logoImageDimensions');
                
                if (previewImage) previewImage.src = dataURL;
                if (fileName) fileName.textContent = 'test-logo.png';
                if (fileType) fileType.textContent = 'image/png';
                if (fileSize) fileSize.textContent = '5.0 KB';
                if (imageDimensions) imageDimensions.textContent = '200 × 100 px';
                if (previewContainer) previewContainer.style.display = 'block';
                
                logTest('Test logo uploaded and preview shown');
            } catch (error) {
                logTest(`Failed to upload test logo: ${error.message}`, false);
            }
        }

        function testWithoutCustomer() {
            try {
                // Clear customer fields
                document.getElementById('customerName').value = '';
                document.getElementById('customerPhone').value = '';
                document.getElementById('customerEmail').value = '';
                document.getElementById('customerAddress').value = '';
                
                generateReceipt();
                logTest('Receipt generated without customer info (handwritten fields)');
            } catch (error) {
                logTest(`Failed to generate receipt without customer: ${error.message}`, false);
            }
        }

        function testWithCustomer() {
            try {
                // Fill customer fields
                document.getElementById('customerName').value = 'John Doe';
                document.getElementById('customerPhone').value = '555-1234';
                document.getElementById('customerEmail').value = '<EMAIL>';
                document.getElementById('customerAddress').value = '123 Main St';
                
                generateReceipt();
                logTest('Receipt generated with customer info');
            } catch (error) {
                logTest(`Failed to generate receipt with customer: ${error.message}`, false);
            }
        }

        function generateTestReceipt() {
            try {
                // Add test item if none exist
                const items = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
                if (items.length === 0) {
                    testAddItem();
                }
                
                // Upload test logo if none exists
                if (!window.currentLogo) {
                    testUploadLogo();
                }
                
                // Generate receipt
                generateReceipt();
                logTest('Complete test receipt generated with all features');
            } catch (error) {
                logTest(`Failed to generate test receipt: ${error.message}`, false);
            }
        }

        function clearAll() {
            try {
                // Clear items
                if (typeof clearAllItems === 'function') {
                    clearAllItems();
                }
                
                // Clear customer info
                document.getElementById('customerName').value = '';
                document.getElementById('customerPhone').value = '';
                document.getElementById('customerEmail').value = '';
                document.getElementById('customerAddress').value = '';
                document.getElementById('notes').value = '';
                
                // Clear logo
                if (typeof removeLogo === 'function') {
                    removeLogo();
                }
                
                logTest('All data cleared');
            } catch (error) {
                logTest(`Failed to clear all: ${error.message}`, false);
            }
        }

        function runAllTests() {
            clearTestResults();
            logTest('Starting comprehensive feature tests...');
            
            // Test 1: Check if date field is removed
            const dateField = document.getElementById('receiptDate');
            if (!dateField) {
                logTest('✅ Date Issued field completely removed');
                document.getElementById('check3').checked = true;
            } else {
                logTest('❌ Date Issued field still exists', false);
            }
            
            // Test 2: Test customer name not required
            try {
                testWithoutCustomer();
                logTest('✅ Customer name not required');
                document.getElementById('check1').checked = true;
            } catch (error) {
                logTest('❌ Customer name requirement test failed', false);
            }
            
            // Test 3: Test logo functionality
            try {
                testUploadLogo();
                setTimeout(() => {
                    generateTestReceipt();
                    setTimeout(() => {
                        const receiptContent = document.getElementById('receiptPreview').innerHTML;
                        if (receiptContent.includes('<img src="data:image/png')) {
                            logTest('✅ Logo appears in receipt preview');
                            document.getElementById('check2').checked = true;
                        } else {
                            logTest('❌ Logo not found in receipt preview', false);
                        }
                        
                        // Test 4: Check for handwritten spaces
                        if (receiptContent.includes('handwrite-space')) {
                            logTest('✅ Customer info shows blank lines for handwriting');
                            document.getElementById('check4').checked = true;
                        } else {
                            logTest('❌ Handwritten spaces not found', false);
                        }
                        
                        // Test 5: Check payment method buttons
                        if (receiptContent.includes('payment-option-button') && 
                            receiptContent.includes('Cash') && 
                            receiptContent.includes('Venmo') && 
                            receiptContent.includes('Zelle') && 
                            receiptContent.includes('Square') && 
                            receiptContent.includes('Stripe')) {
                            logTest('✅ Payment method buttons found (Cash, Venmo, Zelle, Square, Stripe)');
                            document.getElementById('check5').checked = true;
                        } else {
                            logTest('❌ Payment method buttons not found or incomplete', false);
                        }
                        
                        // Test 6: Check signature area
                        if (receiptContent.includes('signature-labels-row') && 
                            receiptContent.includes('signature-lines-area')) {
                            logTest('✅ Signature area with labels on same line and space below');
                            document.getElementById('check6').checked = true;
                        } else {
                            logTest('❌ Signature area not properly formatted', false);
                        }
                        
                        // Test 7: Check certificate border
                        if (receiptContent.includes('receipt-container')) {
                            logTest('✅ Certificate-style border container found');
                            document.getElementById('check7').checked = true;
                        } else {
                            logTest('❌ Certificate border not found', false);
                        }
                        
                        logTest('All feature tests completed!');
                    }, 500);
                }, 500);
            } catch (error) {
                logTest(`Feature tests failed: ${error.message}`, false);
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Complete features test page loaded');
            
            setTimeout(() => {
                logTest('All modules loaded and ready for testing');
            }, 1000);
        });
    </script>
</body>
</html>
