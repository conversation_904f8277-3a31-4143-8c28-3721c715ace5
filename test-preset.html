<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preset Functionality Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Preset Functionality Test</h1>
        
        <!-- Test Controls -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Test Controls</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary me-2" onclick="testShowPresetModal()">Show Preset Modal</button>
                <button class="btn btn-secondary me-2" onclick="testModuleAvailability()">Check Module Availability</button>
                <button class="btn btn-info me-2" onclick="testAddItemFunction()">Test Add Item Function</button>
                <button class="btn btn-success" onclick="clearTestResults()">Clear Results</button>
            </div>
        </div>

        <!-- Receipt Items Display -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Receipt Items</h5>
            </div>
            <div class="card-body">
                <div id="receiptItemsList" class="receipt-items-container">
                    <div class="receipt-items-empty">
                        <i class="fas fa-inbox"></i>
                        <p>No items added yet</p>
                    </div>
                </div>
                <div id="receiptTotals" class="mt-3"></div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card">
            <div class="card-header">
                <h5>Test Results</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">Test results will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Preset Modal (copied from main index.html) -->
    <div class="modal fade" id="presetModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-lang="select_preset">Select Preset Items</h5>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success btn-sm" onclick="showAddPresetForm()">
                            <i class="fas fa-plus me-1"></i>
                            <span data-lang="add_preset">Add Preset</span>
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <!-- Add Preset Form -->
                    <div id="addPresetForm" class="card mb-3" style="display: none;">
                        <div class="card-header">
                            <h6 class="mb-0" data-lang="add_preset">Add Preset Item</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-lang="item_name">Item Name</label>
                                    <input type="text" class="form-control" id="presetItemName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-lang="item_category">Category</label>
                                    <select class="form-select" id="presetItemCategory">
                                        <option value="PC Case">PC Case</option>
                                        <option value="CPU">CPU</option>
                                        <option value="GPU">GPU</option>
                                        <option value="RAM">RAM</option>
                                        <option value="Storage">Storage</option>
                                        <option value="Motherboard">Motherboard</option>
                                        <option value="PSU">PSU</option>
                                        <option value="Cooling">Cooling</option>
                                        <option value="Accessories">Accessories</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label" data-lang="original_price">Original Price</label>
                                    <input type="number" class="form-control" id="presetItemOriginalPrice" min="0" step="0.01">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label" data-lang="special_price">Special Price</label>
                                    <input type="number" class="form-control" id="presetItemSpecialPrice" min="0" step="0.01">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label" data-lang="discount_percentage">Discount %</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="presetItemDiscountPercent" readonly>
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label" data-lang="item_description">Description</label>
                                    <input type="text" class="form-control" id="presetItemDescription">
                                </div>
                                <div class="col-12">
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-success btn-sm" onclick="savePresetItem()">
                                            <i class="fas fa-save me-1"></i>
                                            <span data-lang="save">Save</span>
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="cancelAddPreset()">
                                            <i class="fas fa-times me-1"></i>
                                            <span data-lang="cancel">Cancel</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="presetSearch" placeholder="Search items..." onkeyup="filterPresets()">
                        </div>
                        <div class="col-md-6">
                            <select class="form-select" id="presetCategoryFilter" onchange="filterPresets()">
                                <option value="">All Categories</option>
                                <option value="PC Case">PC Case</option>
                                <option value="CPU">CPU</option>
                                <option value="GPU">GPU</option>
                                <option value="RAM">RAM</option>
                                <option value="Storage">Storage</option>
                                <option value="Motherboard">Motherboard</option>
                                <option value="PSU">PSU</option>
                                <option value="Cooling">Cooling</option>
                                <option value="Accessories">Accessories</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Preset Items List -->
                    <div id="presetItemsList">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                            <p>Loading preset items...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="close">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS - Load in order -->
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>
    
    <!-- Core modules (load first) -->
    <script src="js/core/app-initializer.js"></script>
    
    <!-- Feature modules -->
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>
    
    <!-- Main coordinator -->
    <script src="js/main-new.js"></script>
    
    <!-- Legacy modules that depend on new system -->
    <script src="js/preset-manager.js"></script>

    <!-- Debug script -->
    <script src="debug-preset.js"></script>

    <!-- Test Functions -->
    <script>
        let testCounter = 0;

        function logTest(message, success = true) {
            testCounter++;
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = success ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>';
            resultsDiv.innerHTML += `<div class="mb-1">${statusIcon} [${timestamp}] Test ${testCounter}: ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearTestResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-muted">Test results will appear here...</p>';
            testCounter = 0;
        }

        function testModuleAvailability() {
            logTest(`AppInitializer available: ${typeof window.AppInitializer !== 'undefined'}`);
            logTest(`ItemManager available: ${typeof window.ItemManager !== 'undefined'}`);
            logTest(`ReceiptGenerator available: ${typeof window.ReceiptGenerator !== 'undefined'}`);
            logTest(`UIManager available: ${typeof window.UIManager !== 'undefined'}`);
            logTest(`presetManager available: ${typeof window.presetManager !== 'undefined'}`);
            logTest(`addItemToReceipt function available: ${typeof window.addItemToReceipt === 'function'}`);
            logTest(`showPresetModal function available: ${typeof window.showPresetModal === 'function'}`);
        }

        function testShowPresetModal() {
            try {
                if (typeof window.showPresetModal === 'function') {
                    window.showPresetModal();
                    logTest('Preset modal opened successfully');
                } else {
                    logTest('showPresetModal function not available', false);
                }
            } catch (error) {
                logTest(`Failed to open preset modal: ${error.message}`, false);
            }
        }

        function testAddItemFunction() {
            try {
                const testItem = {
                    id: 999,
                    name: 'Test PC Component',
                    category: 'Test Category',
                    description: 'This is a test component',
                    default_price: 199.99,
                    special_price: 149.99,
                    original_price: 249.99
                };
                
                if (typeof window.addItemToReceipt === 'function') {
                    window.addItemToReceipt(testItem);
                    logTest('Test item added successfully');
                } else {
                    logTest('addItemToReceipt function not available', false);
                }
            } catch (error) {
                logTest(`Failed to add test item: ${error.message}`, false);
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Test page loaded');
            
            // Test module availability after a short delay
            setTimeout(() => {
                testModuleAvailability();
            }, 500);
        });
    </script>
</body>
</html>
