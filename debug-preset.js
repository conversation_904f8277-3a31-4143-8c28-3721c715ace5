/**
 * Debug script for preset functionality
 * Add this script to test pages to diagnose preset issues
 */

function debugPresetFunctionality() {
    console.log('=== Preset Functionality Debug ===');
    
    // Check module availability
    console.log('Module Availability:');
    console.log('- AppInitializer:', typeof window.AppInitializer);
    console.log('- ItemManager:', typeof window.ItemManager);
    console.log('- ReceiptGenerator:', typeof window.ReceiptGenerator);
    console.log('- UIManager:', typeof window.UIManager);
    console.log('- presetManager:', typeof window.presetManager);
    
    // Check function availability
    console.log('\nFunction Availability:');
    console.log('- addItemToReceipt:', typeof window.addItemToReceipt);
    console.log('- showPresetModal:', typeof window.showPresetModal);
    console.log('- showMessage:', typeof window.showMessage);
    
    // Check ItemManager methods
    if (window.ItemManager) {
        console.log('\nItemManager Methods:');
        console.log('- addItemToReceipt:', typeof window.ItemManager.addItemToReceipt);
        console.log('- receiptItems length:', window.ItemManager.receiptItems ? window.ItemManager.receiptItems.length : 'N/A');
    }
    
    // Check presetManager methods
    if (window.presetManager) {
        console.log('\npresetManager Methods:');
        console.log('- showPresetModal:', typeof window.presetManager.showPresetModal);
        console.log('- selectPresetItem:', typeof window.presetManager.selectPresetItem);
        console.log('- pcParts length:', window.presetManager.pcParts ? window.presetManager.pcParts.length : 'N/A');
    }
    
    // Check DOM elements
    console.log('\nDOM Elements:');
    console.log('- presetModal:', !!document.getElementById('presetModal'));
    console.log('- receiptItemsList:', !!document.getElementById('receiptItemsList'));
    console.log('- presetItemsList:', !!document.getElementById('presetItemsList'));
    
    console.log('=== End Debug ===');
}

function testPresetItemAddition() {
    console.log('=== Testing Preset Item Addition ===');
    
    const testItem = {
        id: 999,
        name: 'Debug Test Item',
        category: 'Test',
        description: 'This is a debug test item',
        default_price: 99.99,
        special_price: 79.99,
        original_price: 119.99
    };
    
    console.log('Test item:', testItem);
    
    try {
        if (typeof window.addItemToReceipt === 'function') {
            console.log('Calling window.addItemToReceipt...');
            window.addItemToReceipt(testItem);
            console.log('✓ Item added via window.addItemToReceipt');
        } else if (window.ItemManager && typeof window.ItemManager.addItemToReceipt === 'function') {
            console.log('Calling window.ItemManager.addItemToReceipt...');
            window.ItemManager.addItemToReceipt(testItem);
            console.log('✓ Item added via ItemManager');
        } else {
            console.error('✗ No addItemToReceipt function available');
        }
    } catch (error) {
        console.error('✗ Error adding item:', error);
    }
    
    console.log('=== End Test ===');
}

function testPresetModalOpening() {
    console.log('=== Testing Preset Modal Opening ===');
    
    try {
        if (typeof window.showPresetModal === 'function') {
            console.log('Calling window.showPresetModal...');
            window.showPresetModal();
            console.log('✓ Modal opened via window.showPresetModal');
        } else if (window.presetManager && typeof window.presetManager.showPresetModal === 'function') {
            console.log('Calling window.presetManager.showPresetModal...');
            window.presetManager.showPresetModal();
            console.log('✓ Modal opened via presetManager');
        } else {
            console.error('✗ No showPresetModal function available');
        }
    } catch (error) {
        console.error('✗ Error opening modal:', error);
    }
    
    console.log('=== End Test ===');
}

// Auto-run debug when script loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        debugPresetFunctionality();
    }, 1000);
});

// Export functions to global scope for manual testing
window.debugPresetFunctionality = debugPresetFunctionality;
window.testPresetItemAddition = testPresetItemAddition;
window.testPresetModalOpening = testPresetModalOpening;
