<?php
/**
 * 資料庫初始化腳本
 * KMS PC Receipt Maker
 */

require_once 'config.php';

// 設置錯誤報告
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS PC Receipt Maker - 資料庫初始化</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .step-indicator {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .step-success {
            background: #d4edda;
            color: #155724;
        }
        .step-error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="setup-card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            KMS PC Receipt Maker - 資料庫初始化
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php
                        $steps = [];
                        $hasError = false;

                        try {
                            // 步驟 1: 測試資料庫連接
                            $steps[] = ['name' => '測試資料庫連接', 'status' => 'running'];
                            
                            $connection = new mysqli(DB_HOST, DB_USER, DB_PASS);
                            
                            if ($connection->connect_error) {
                                throw new Exception('無法連接到 MySQL 伺服器: ' . $connection->connect_error);
                            }
                            
                            $steps[count($steps) - 1]['status'] = 'success';
                            $steps[count($steps) - 1]['message'] = '成功連接到 MySQL 伺服器';

                            // 步驟 2: 創建資料庫
                            $steps[] = ['name' => '創建資料庫', 'status' => 'running'];
                            
                            $createDbSql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                            
                            if (!$connection->query($createDbSql)) {
                                throw new Exception('創建資料庫失敗: ' . $connection->error);
                            }
                            
                            $steps[count($steps) - 1]['status'] = 'success';
                            $steps[count($steps) - 1]['message'] = '資料庫 ' . DB_NAME . ' 創建成功';

                            // 步驟 3: 選擇資料庫
                            $steps[] = ['name' => '選擇資料庫', 'status' => 'running'];
                            
                            if (!$connection->select_db(DB_NAME)) {
                                throw new Exception('選擇資料庫失敗: ' . $connection->error);
                            }
                            
                            $connection->set_charset(DB_CHARSET);
                            
                            $steps[count($steps) - 1]['status'] = 'success';
                            $steps[count($steps) - 1]['message'] = '成功選擇資料庫並設置字符集';

                            // 步驟 4: 讀取並執行 SQL 腳本
                            $steps[] = ['name' => '執行資料庫腳本', 'status' => 'running'];
                            
                            $sqlFile = __DIR__ . '/../database/setup.sql';
                            
                            if (!file_exists($sqlFile)) {
                                throw new Exception('找不到 SQL 腳本文件: ' . $sqlFile);
                            }
                            
                            $sql = file_get_contents($sqlFile);
                            
                            // 移除 CREATE DATABASE 和 USE 語句，因為我們已經處理了
                            $sql = preg_replace('/CREATE DATABASE.*?;/i', '', $sql);
                            $sql = preg_replace('/USE.*?;/i', '', $sql);
                            
                            // 分割 SQL 語句
                            $statements = array_filter(array_map('trim', explode(';', $sql)));
                            
                            $executedStatements = 0;
                            foreach ($statements as $statement) {
                                if (!empty($statement)) {
                                    if (!$connection->query($statement)) {
                                        throw new Exception('執行 SQL 語句失敗: ' . $connection->error . "\n語句: " . $statement);
                                    }
                                    $executedStatements++;
                                }
                            }
                            
                            $steps[count($steps) - 1]['status'] = 'success';
                            $steps[count($steps) - 1]['message'] = "成功執行 {$executedStatements} 個 SQL 語句";

                            // 步驟 5: 驗證資料表
                            $steps[] = ['name' => '驗證資料表', 'status' => 'running'];
                            
                            $tables = ['receipts', 'receipt_items', 'pc_parts', 'receipt_configurations'];
                            $createdTables = [];
                            
                            foreach ($tables as $table) {
                                $result = $connection->query("SHOW TABLES LIKE '{$table}'");
                                if ($result && $result->num_rows > 0) {
                                    $createdTables[] = $table;
                                }
                            }
                            
                            if (count($createdTables) !== count($tables)) {
                                $missingTables = array_diff($tables, $createdTables);
                                throw new Exception('缺少資料表: ' . implode(', ', $missingTables));
                            }
                            
                            $steps[count($steps) - 1]['status'] = 'success';
                            $steps[count($steps) - 1]['message'] = '所有資料表創建成功: ' . implode(', ', $createdTables);

                            // 步驟 6: 檢查預設數據
                            $steps[] = ['name' => '檢查預設數據', 'status' => 'running'];
                            
                            $result = $connection->query("SELECT COUNT(*) as count FROM pc_parts");
                            $row = $result->fetch_assoc();
                            $partsCount = $row['count'];
                            
                            $steps[count($steps) - 1]['status'] = 'success';
                            $steps[count($steps) - 1]['message'] = "預設電腦零件數據載入成功，共 {$partsCount} 個項目";

                            $connection->close();

                        } catch (Exception $e) {
                            $hasError = true;
                            if (isset($steps[count($steps) - 1])) {
                                $steps[count($steps) - 1]['status'] = 'error';
                                $steps[count($steps) - 1]['message'] = $e->getMessage();
                            }
                        }

                        // 顯示步驟結果
                        foreach ($steps as $step) {
                            $statusClass = '';
                            $statusIcon = '';
                            
                            switch ($step['status']) {
                                case 'success':
                                    $statusClass = 'step-success';
                                    $statusIcon = 'fas fa-check-circle text-success';
                                    break;
                                case 'error':
                                    $statusClass = 'step-error';
                                    $statusIcon = 'fas fa-times-circle text-danger';
                                    break;
                                default:
                                    $statusClass = '';
                                    $statusIcon = 'fas fa-spinner fa-spin text-primary';
                            }
                            
                            echo "<div class='step-indicator {$statusClass}'>";
                            echo "<div class='d-flex align-items-center'>";
                            echo "<i class='{$statusIcon} me-2'></i>";
                            echo "<strong>{$step['name']}</strong>";
                            echo "</div>";
                            
                            if (isset($step['message'])) {
                                echo "<div class='mt-2'><small>{$step['message']}</small></div>";
                            }
                            
                            echo "</div>";
                        }

                        if (!$hasError) {
                            echo "<div class='alert alert-success mt-3'>";
                            echo "<h5><i class='fas fa-check-circle me-2'></i>安裝完成！</h5>";
                            echo "<p>資料庫初始化成功完成。您現在可以開始使用 KMS PC Receipt Maker。</p>";
                            echo "<a href='../index.html' class='btn btn-success'>";
                            echo "<i class='fas fa-arrow-right me-1'></i>開始使用";
                            echo "</a>";
                            echo "</div>";
                        } else {
                            echo "<div class='alert alert-danger mt-3'>";
                            echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>安裝失敗</h5>";
                            echo "<p>資料庫初始化過程中發生錯誤。請檢查錯誤訊息並修復問題後重試。</p>";
                            echo "<button class='btn btn-danger' onclick='location.reload()'>";
                            echo "<i class='fas fa-redo me-1'></i>重試";
                            echo "</button>";
                            echo "</div>";
                        }
                        ?>
                        
                        <div class="mt-4">
                            <h6>系統配置信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>資料庫主機:</strong></td>
                                    <td><?php echo DB_HOST; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>資料庫名稱:</strong></td>
                                    <td><?php echo DB_NAME; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>字符集:</strong></td>
                                    <td><?php echo DB_CHARSET; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>公司名稱:</strong></td>
                                    <td><?php echo COMPANY_NAME; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>應用版本:</strong></td>
                                    <td><?php echo APP_VERSION; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>