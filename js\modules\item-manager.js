/**
 * Item Manager
 * Handles receipt items CRUD operations and drag-and-drop functionality
 */

class ItemManager {
    constructor() {
        this.receiptItems = [];
        this.isDragDropEnabled = true;
        this.compactView = true;
    }

    /**
     * Add Item to Receipt
     */
    addItemToReceipt(item) {
        // Price priority: Special price (>0 and less than original if exists) -> Default price -> Original price -> 0
        const hasNum = (v) => typeof v === 'number' && !isNaN(v);
        const useSpecial = hasNum(item?.special_price) && item.special_price > 0 && (!hasNum(item?.original_price) || item.special_price < item.original_price);
        const basePrice = useSpecial
            ? item.special_price
            : (hasNum(item?.default_price) ? item.default_price : (hasNum(item?.original_price) ? item.original_price : 0));

        // Calculate discount percentage
        let discountPercent = 0;
        if (hasNum(item?.original_price) && hasNum(item?.special_price) && item.original_price > 0) {
            discountPercent = Math.round(((item.original_price - item.special_price) / item.original_price) * 100);
        }

        const newItem = {
            id: Date.now(),
            name: item.name,
            category: item.category,
            description: item.description || '',
            quantity: 1,
            unitPrice: parseFloat(basePrice) || 0,
            totalPrice: parseFloat(basePrice) || 0,
            originalPrice: parseFloat(item.original_price) || 0,
            specialPrice: parseFloat(item.special_price) || 0,
            discountPercent: discountPercent,
            hidePrice: false
        };

        this.receiptItems.push(newItem);
        this.updateReceiptItemsDisplay();
        this.updateTotals();
        
        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        } else if (typeof updateReceiptPreview === 'function') {
            updateReceiptPreview();
        }

        // Show success message
        if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
            UIManager.showMessage('Item added successfully!', 'success');
        } else if (typeof showMessage === 'function') {
            showMessage('Item added successfully!', 'success');
        }
    }

    /**
     * Remove Item from Receipt
     */
    removeItemFromReceipt(itemId) {
        this.receiptItems = this.receiptItems.filter(item => item.id !== itemId);
        this.updateReceiptItemsDisplay();
        this.updateTotals();

        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        } else if (typeof updateReceiptPreview === 'function') {
            updateReceiptPreview();
        }
    }

    /**
     * Update Item Quantity
     */
    updateItemQuantity(itemId, quantity) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (item) {
            item.quantity = Math.max(1, parseInt(quantity) || 1);
            item.totalPrice = item.quantity * item.unitPrice;
            this.updateReceiptItemsDisplay();
            this.updateTotals();
            
            if (typeof ReceiptGenerator !== 'undefined') {
                ReceiptGenerator.updateReceiptPreview();
            }
        }
    }

    /**
     * Update Item Price
     */
    updateItemPrice(itemId, price) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (item) {
            item.unitPrice = Math.max(0, parseFloat(price) || 0);
            item.totalPrice = item.quantity * item.unitPrice;
            this.updateReceiptItemsDisplay();
            this.updateTotals();
            
            if (typeof ReceiptGenerator !== 'undefined') {
                ReceiptGenerator.updateReceiptPreview();
            }
        }
    }

    /**
     * Toggle Item Price Display
     */
    toggleItemPriceDisplay(itemId) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (item) {
            item.hidePrice = !item.hidePrice;
            this.updateReceiptItemsDisplay();

            if (typeof ReceiptGenerator !== 'undefined') {
                ReceiptGenerator.updateReceiptPreview();
            }
        }
    }

    /**
     * Render Price Information
     */
    renderPriceInfo(item) {
        if (!item.originalPrice && !item.specialPrice) {
            return '';
        }

        let priceHtml = '<div class="item-price-info mt-1">';

        if (item.originalPrice > 0 && item.specialPrice > 0 && item.specialPrice < item.originalPrice) {
            // Show original price crossed out and special price
            priceHtml += `
                <div class="price-display">
                    <span class="original-price text-muted text-decoration-line-through">$${item.originalPrice.toFixed(2)}</span>
                    <span class="special-price text-danger fw-bold ms-2">$${item.specialPrice.toFixed(2)}</span>
                    ${item.discountPercent > 0 ? `<span class="discount-badge badge bg-success ms-2">-${item.discountPercent}%</span>` : ''}
                </div>
            `;
        } else if (item.originalPrice > 0) {
            priceHtml += `<div class="price-display"><span class="original-price">$${item.originalPrice.toFixed(2)}</span></div>`;
        }

        priceHtml += '</div>';
        return priceHtml;
    }

    /**
     * Edit Item
     */
    editItem(itemId) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (!item) return;

        // Create edit modal HTML
        const modalHtml = `
            <div class="modal fade" id="editItemModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Edit Item</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Item Name</label>
                                <input type="text" class="form-control" id="editItemName" value="${item.name}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Category</label>
                                <input type="text" class="form-control" id="editItemCategory" value="${item.category}">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <input type="text" class="form-control" id="editItemDescription" value="${item.description || ''}">
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Original Price</label>
                                    <input type="number" class="form-control" id="editItemOriginalPrice" value="${item.originalPrice || ''}" min="0" step="0.01">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Special Price</label>
                                    <input type="number" class="form-control" id="editItemSpecialPrice" value="${item.specialPrice || ''}" min="0" step="0.01">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Quantity</label>
                                    <input type="number" class="form-control" id="editItemQuantity" value="${item.quantity}" min="1">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Unit Price</label>
                                    <input type="number" class="form-control" id="editItemUnitPrice" value="${item.unitPrice}" min="0" step="0.01">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="saveItemEdit(${itemId})">Save Changes</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('editItemModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('editItemModal'));
        modal.show();
    }

    /**
     * Save Item Edit
     */
    saveItemEdit(itemId) {
        const item = this.receiptItems.find(item => item.id === itemId);
        if (!item) return;

        // Get values from form
        const name = document.getElementById('editItemName').value.trim();
        const category = document.getElementById('editItemCategory').value.trim();
        const description = document.getElementById('editItemDescription').value.trim();
        const originalPrice = parseFloat(document.getElementById('editItemOriginalPrice').value) || 0;
        const specialPrice = parseFloat(document.getElementById('editItemSpecialPrice').value) || 0;
        const quantity = parseInt(document.getElementById('editItemQuantity').value) || 1;
        const unitPrice = parseFloat(document.getElementById('editItemUnitPrice').value) || 0;

        // Calculate discount percentage
        let discountPercent = 0;
        if (originalPrice > 0 && specialPrice > 0 && originalPrice > specialPrice) {
            discountPercent = Math.round(((originalPrice - specialPrice) / originalPrice) * 100);
        }

        // Update item
        item.name = name;
        item.category = category;
        item.description = description;
        item.originalPrice = originalPrice;
        item.specialPrice = specialPrice;
        item.discountPercent = discountPercent;
        item.quantity = quantity;
        item.unitPrice = unitPrice;
        item.totalPrice = quantity * unitPrice;

        // Update display
        this.updateReceiptItemsDisplay();
        this.updateTotals();

        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        }

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('editItemModal'));
        if (modal) {
            modal.hide();
        }

        // Show success message
        if (typeof UIManager !== 'undefined') {
            UIManager.showMessage('Item updated successfully!', 'success');
        }
    }

    /**
     * Move Item (for drag and drop)
     */
    moveItem(fromIndex, toIndex) {
        if (fromIndex < 0 || fromIndex >= this.receiptItems.length || 
            toIndex < 0 || toIndex >= this.receiptItems.length) {
            return;
        }

        const item = this.receiptItems.splice(fromIndex, 1)[0];
        this.receiptItems.splice(toIndex, 0, item);
        
        this.updateReceiptItemsDisplay();
        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        } else if (typeof updateReceiptPreview === 'function') {
            updateReceiptPreview();
        }
    }

    /**
     * Update Receipt Items Display
     */
    updateReceiptItemsDisplay() {
        const container = document.getElementById('receiptItemsList');
        if (!container) return;

        if (this.receiptItems.length === 0) {
            container.innerHTML = `
                <div class="receipt-items-empty">
                    <i class="fas fa-inbox"></i>
                    <p>No items added yet</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.receiptItems.map((item, index) => `
            <div class="receipt-item ${this.isDragDropEnabled ? 'draggable' : ''}" 
                 data-item-id="${item.id}" 
                 data-index="${index}"
                 draggable="${this.isDragDropEnabled}">
                ${this.isDragDropEnabled ? '<div class="drag-handle"><i class="fas fa-grip-vertical"></i></div>' : ''}
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="item-info">
                            <h6>${item.name}</h6>
                            <div class="item-category">${item.category}</div>
                            ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                            ${this.renderPriceInfo(item)}
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Quantity</label>
                        <input type="number" class="form-control form-control-sm"
                               value="${item.quantity}" min="1"
                               onchange="ItemManager.updateItemQuantity(${item.id}, this.value)">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Unit Price</label>
                        <input type="number" class="form-control form-control-sm"
                               value="${item.unitPrice}" min="0" step="0.01"
                               onchange="ItemManager.updateItemPrice(${item.id}, this.value)">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Total Price</label>
                        <div class="item-total-price">$${item.totalPrice.toFixed(2)}</div>
                    </div>
                    <div class="col-md-2">
                        <div class="item-actions">
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="editItem(${item.id})"
                                    title="Edit Item">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary"
                                    onclick="toggleItemPriceDisplay(${item.id})"
                                    title="${item.hidePrice ? 'Show Price' : 'Hide Price'}">
                                <i class="fas fa-${item.hidePrice ? 'eye' : 'eye-slash'}"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger"
                                    onclick="removeItemFromReceipt(${item.id})"
                                    title="Remove Item">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        // Initialize drag and drop if enabled
        if (this.isDragDropEnabled) {
            this.initializeDragAndDrop();
        }
    }

    /**
     * Initialize Drag and Drop
     */
    initializeDragAndDrop() {
        const container = document.getElementById('receiptItemsList');
        if (!container) return;

        const items = container.querySelectorAll('.receipt-item.draggable');
        
        items.forEach(item => {
            item.addEventListener('dragstart', this.handleDragStart.bind(this));
            item.addEventListener('dragover', this.handleDragOver.bind(this));
            item.addEventListener('drop', this.handleDrop.bind(this));
            item.addEventListener('dragend', this.handleDragEnd.bind(this));
        });
    }

    /**
     * Handle Drag Start
     */
    handleDragStart(e) {
        e.dataTransfer.setData('text/plain', e.target.dataset.index);
        e.target.classList.add('dragging');
    }

    /**
     * Handle Drag Over
     */
    handleDragOver(e) {
        e.preventDefault();
        e.target.closest('.receipt-item').classList.add('drag-over');
    }

    /**
     * Handle Drop
     */
    handleDrop(e) {
        e.preventDefault();
        const fromIndex = parseInt(e.dataTransfer.getData('text/plain'));
        const toIndex = parseInt(e.target.closest('.receipt-item').dataset.index);
        
        if (fromIndex !== toIndex) {
            this.moveItem(fromIndex, toIndex);
        }
        
        // Clean up drag classes
        document.querySelectorAll('.receipt-item').forEach(item => {
            item.classList.remove('drag-over');
        });
    }

    /**
     * Handle Drag End
     */
    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        document.querySelectorAll('.receipt-item').forEach(item => {
            item.classList.remove('drag-over');
        });
    }

    /**
     * Update Totals
     */
    updateTotals() {
        const subtotal = this.receiptItems.reduce((sum, item) => sum + item.totalPrice, 0);
        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value) || 0;
        const taxRate = parseFloat(document.getElementById('taxRate')?.value) || 0;

        const discountedSubtotal = Math.max(0, subtotal - discountAmount);
        const taxAmount = discountedSubtotal * (taxRate / 100);
        const total = discountedSubtotal + taxAmount;

        this.updateTotalsDisplay(subtotal, discountAmount, taxAmount, total);
    }

    /**
     * Update Totals Display
     */
    updateTotalsDisplay(subtotal, discount, tax, total) {
        const totalsContainer = document.getElementById('receiptTotals');
        if (totalsContainer) {
            totalsContainer.innerHTML = `
                <div class="receipt-totals-summary">
                    <div class="row">
                        <div class="col-6">Subtotal:</div>
                        <div class="col-6">$${subtotal.toFixed(2)}</div>
                    </div>
                    ${discount > 0 ? `
                    <div class="row">
                        <div class="col-6">Discount:</div>
                        <div class="col-6 text-danger">-$${discount.toFixed(2)}</div>
                    </div>
                    ` : ''}
                    ${tax > 0 ? `
                    <div class="row">
                        <div class="col-6">Tax:</div>
                        <div class="col-6">$${tax.toFixed(2)}</div>
                    </div>
                    ` : ''}
                    <div class="row">
                        <div class="col-6">Total:</div>
                        <div class="col-6">$${total.toFixed(2)}</div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Calculate Totals
     */
    calculateTotals() {
        const subtotal = this.receiptItems.reduce((sum, item) => sum + item.totalPrice, 0);
        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value) || 0;
        const taxRate = parseFloat(document.getElementById('taxRate')?.value) || 0;

        const discountedSubtotal = Math.max(0, subtotal - discountAmount);
        const taxAmount = discountedSubtotal * (taxRate / 100);
        const total = discountedSubtotal + taxAmount;

        return {
            subtotal: subtotal,
            discount: discountAmount,
            tax: taxAmount,
            total: total
        };
    }

    /**
     * Clear All Items
     */
    clearAllItems() {
        if (this.receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('No items to clear', 'info');
            }
            return;
        }

        if (confirm('Are you sure you want to clear all items?')) {
            this.receiptItems = [];
            this.updateReceiptItemsDisplay();
            this.updateTotals();
            
            if (typeof ReceiptGenerator !== 'undefined') {
                ReceiptGenerator.updateReceiptPreview();
            }
            
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('All items cleared', 'info');
            }
        }
    }

    /**
     * Get receipt items
     */
    getReceiptItems() {
        return this.receiptItems;
    }

    /**
     * Clear all receipt items
     */
    clearReceiptItems() {
        this.receiptItems = [];
        this.updateReceiptItemsDisplay();
        this.updateTotals();

        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        } else if (typeof updateReceiptPreview === 'function') {
            updateReceiptPreview();
        }
    }

    /**
     * Add receipt item (alias for addItemToReceipt)
     */
    addReceiptItem(item) {
        return this.addItemToReceipt(item);
    }

    /**
     * Set receipt items
     */
    setReceiptItems(items) {
        this.receiptItems = items || [];
        this.updateReceiptItemsDisplay();
        this.updateTotals();
    }

    /**
     * Toggle compact view
     */
    toggleCompactView() {
        this.compactView = !this.compactView;
        const container = document.getElementById('receiptItemsList');
        if (container) {
            container.classList.toggle('compact-view', this.compactView);
        }
    }

    /**
     * Toggle drag and drop
     */
    toggleDragAndDrop() {
        this.isDragDropEnabled = !this.isDragDropEnabled;
        this.updateReceiptItemsDisplay();
    }

    /**
     * Show Add Item Modal
     */
    showAddItemModal() {
        // Clear form
        const nameEl = document.getElementById('modalItemName');
        const categoryEl = document.getElementById('modalItemCategory');
        const descEl = document.getElementById('modalItemDescription');
        const quantityEl = document.getElementById('modalItemQuantity');
        const originalPriceEl = document.getElementById('modalItemOriginalPrice');
        const specialPriceEl = document.getElementById('modalItemSpecialPrice');
        const hidePriceEl = document.getElementById('modalItemHidePrice');

        if (nameEl) nameEl.value = '';
        if (categoryEl) categoryEl.value = 'PC Case';
        if (descEl) descEl.value = '';
        if (quantityEl) quantityEl.value = '1';
        if (originalPriceEl) originalPriceEl.value = '';
        if (specialPriceEl) specialPriceEl.value = '';
        if (hidePriceEl) hidePriceEl.checked = false;

        // Show modal
        const modal = document.getElementById('addItemModal');
        if (modal) {
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
        } else {
            console.error('Add item modal not found');
        }
    }

    /**
     * Confirm Add Modal Item
     */
    confirmAddModalItem() {
        const nameEl = document.getElementById('modalItemName');
        const categoryEl = document.getElementById('modalItemCategory');
        const descEl = document.getElementById('modalItemDescription');
        const quantityEl = document.getElementById('modalItemQuantity');
        const originalPriceEl = document.getElementById('modalItemOriginalPrice');
        const specialPriceEl = document.getElementById('modalItemSpecialPrice');
        const hidePriceEl = document.getElementById('modalItemHidePrice');

        if (!nameEl || !categoryEl || !quantityEl || !specialPriceEl) {
            alert('Required form elements not found');
            return;
        }

        const name = nameEl.value.trim();
        const category = categoryEl.value;
        const description = descEl ? descEl.value.trim() : '';
        const quantity = parseInt(quantityEl.value) || 1;
        const originalPrice = originalPriceEl ? parseFloat(originalPriceEl.value) || 0 : 0;
        const specialPrice = parseFloat(specialPriceEl.value) || 0;
        const hidePrice = hidePriceEl ? hidePriceEl.checked : false;

        if (!name) {
            alert('Please enter item name');
            return;
        }

        if (specialPrice <= 0) {
            alert('Please enter a valid price');
            return;
        }

        // Calculate discount percentage
        let discountPercent = 0;
        if (originalPrice > 0 && specialPrice < originalPrice) {
            discountPercent = Math.round(((originalPrice - specialPrice) / originalPrice) * 100);
        }

        // Create new item
        const newItem = {
            id: Date.now(),
            name: name,
            category: category,
            description: description,
            quantity: quantity,
            unitPrice: specialPrice,
            totalPrice: quantity * specialPrice,
            originalPrice: originalPrice,
            specialPrice: specialPrice,
            discountPercent: discountPercent,
            hidePrice: hidePrice
        };

        // Add to receipt items
        this.receiptItems.push(newItem);
        this.updateReceiptItemsDisplay();
        this.updateTotals();

        if (typeof ReceiptGenerator !== 'undefined' && ReceiptGenerator.updateReceiptPreview) {
            ReceiptGenerator.updateReceiptPreview();
        }

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addItemModal'));
        if (modal) {
            modal.hide();
        }

        // Show success message
        if (typeof UIManager !== 'undefined') {
            UIManager.showMessage('Item added successfully!', 'success');
        }
    }
}

// Create global instance
window.ItemManager = new ItemManager();

// Export functions for backward compatibility
Object.defineProperty(window, 'receiptItems', {
    get: () => window.ItemManager.receiptItems,
    set: (value) => window.ItemManager.setReceiptItems(value)
});
window.addItemToReceipt = (item) => window.ItemManager.addItemToReceipt(item);
window.removeItemFromReceipt = (itemId) => window.ItemManager.removeItemFromReceipt(itemId);
window.updateItemQuantity = (itemId, quantity) => window.ItemManager.updateItemQuantity(itemId, quantity);
window.updateItemPrice = (itemId, price) => window.ItemManager.updateItemPrice(itemId, price);
window.toggleItemPriceDisplay = (itemId) => window.ItemManager.toggleItemPriceDisplay(itemId);
window.updateReceiptItemsDisplay = () => window.ItemManager.updateReceiptItemsDisplay();
window.updateTotals = () => window.ItemManager.updateTotals();
window.calculateTotals = () => window.ItemManager.calculateTotals();
window.clearAllItems = () => window.ItemManager.clearAllItems();
window.showAddItemModal = () => window.ItemManager.showAddItemModal();
window.confirmAddModalItem = () => window.ItemManager.confirmAddModalItem();
window.editItem = (itemId) => window.ItemManager.editItem(itemId);
window.saveItemEdit = (itemId) => window.ItemManager.saveItemEdit(itemId);
