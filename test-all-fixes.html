<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS PC Receipt Maker - Test All Fixes</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/receipt-items.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">KMS PC Receipt Maker - Test All Fixes</h1>
        
        <!-- Fix Summary -->
        <div class="alert alert-info">
            <h5><i class="fas fa-check-circle me-2"></i>Fixes Applied:</h5>
            <ul class="mb-0">
                <li>✅ Cleaned up duplicate CSS definitions in style.css</li>
                <li>✅ Removed "Payment Method:" text from receipt preview</li>
                <li>✅ Ensured only 5 payment options (Cash, Venmo, Zelle, Square, Stripe)</li>
                <li>✅ Added auto-generation for customer name, phone, and email</li>
                <li>✅ Fixed Save Receipt and Print Receipt functions</li>
                <li>✅ Improved CSS path handling for print function</li>
            </ul>
        </div>
        
        <!-- Test Customer Info Auto-Generation -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    Customer Information (Auto-Generated)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="customerName" class="form-label">Customer Name</label>
                        <input type="text" class="form-control" id="customerName" placeholder="Will auto-generate if empty">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerPhone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="customerPhone" placeholder="Will auto-generate if empty">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="customerEmail" placeholder="Will auto-generate if empty">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="customerAddress" class="form-label">Address</label>
                        <input type="text" class="form-control" id="customerAddress" placeholder="Optional">
                    </div>
                </div>
                <button type="button" class="btn btn-primary" onclick="testAutoGeneration()">
                    <i class="fas fa-magic me-1"></i>
                    Test Auto-Generation
                </button>
            </div>
        </div>

        <!-- Test Items -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Test Items
                </h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-success" onclick="addTestItems()">
                    <i class="fas fa-plus me-1"></i>
                    Add Test Items
                </button>
                <div id="itemsList" class="mt-3">
                    <!-- Items will be added here -->
                </div>
            </div>
        </div>

        <!-- Receipt Generation -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    Receipt Generation
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="receiptNumber" class="form-label">Receipt Number</label>
                        <input type="text" class="form-control" id="receiptNumber" placeholder="Auto-generated">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="2" placeholder="Optional notes"></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="discountAmount" class="form-label">Discount Amount</label>
                        <input type="number" class="form-control" id="discountAmount" value="0" min="0" step="0.01">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="taxRate" class="form-label">Tax Rate (%)</label>
                        <input type="number" class="form-control" id="taxRate" value="0" min="0" max="100" step="0.01">
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-primary" onclick="generateReceipt()">
                        <i class="fas fa-file-invoice me-1"></i>
                        Generate Receipt
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveReceipt()" id="saveBtn" disabled>
                        <i class="fas fa-save me-1"></i>
                        Save Receipt
                    </button>
                    <button type="button" class="btn btn-info" onclick="printReceipt()" id="printBtn" disabled>
                        <i class="fas fa-print me-1"></i>
                        Print Receipt
                    </button>
                </div>
            </div>
        </div>

        <!-- Receipt Preview -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>
                    Receipt Preview (No "Payment Method:" text, 5 payment options)
                </h5>
            </div>
            <div class="card-body">
                <div id="receiptPreview">
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="js/modules/ui-manager.js"></script>
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/main-new.js"></script>

    <script>
        // Test functions
        function testAutoGeneration() {
            // Clear fields first
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';
            document.getElementById('customerEmail').value = '';

            // Generate customer info
            if (window.ReceiptGenerator) {
                const name = window.ReceiptGenerator.generateCustomerName();
                const phone = window.ReceiptGenerator.generateCustomerPhone();
                const email = window.ReceiptGenerator.generateCustomerEmail();

                document.getElementById('customerName').value = name;
                document.getElementById('customerPhone').value = phone;
                document.getElementById('customerEmail').value = email;

                alert(`Auto-generated:\nName: ${name}\nPhone: ${phone}\nEmail: ${email}`);
            } else {
                alert('ReceiptGenerator not available');
            }
        }

        function addTestItems() {
            if (window.ItemManager) {
                // Clear existing items first
                window.ItemManager.clearReceiptItems();

                // Add some test items
                const testItems = [
                    { name: 'Intel Core i7-12700K', category: 'CPU', description: '12th Gen Processor', quantity: 1, unitPrice: 400, totalPrice: 400 },
                    { name: 'NVIDIA RTX 4070', category: 'GPU', description: 'Graphics Card', quantity: 1, unitPrice: 600, totalPrice: 600 },
                    { name: 'Corsair 32GB DDR4', category: 'RAM', description: 'Memory Kit', quantity: 1, unitPrice: 150, totalPrice: 150 }
                ];

                testItems.forEach(item => {
                    window.ItemManager.addReceiptItem(item);
                });

                updateItemsDisplay();
                alert('Test items added successfully!');
            } else {
                alert('ItemManager not available');
            }
        }

        function updateItemsDisplay() {
            const itemsList = document.getElementById('itemsList');
            if (window.ItemManager) {
                const items = window.ItemManager.getReceiptItems();
                if (items.length > 0) {
                    itemsList.innerHTML = `
                        <div class="alert alert-success">
                            <strong>${items.length} items added:</strong>
                            <ul class="mb-0 mt-2">
                                ${items.map(item => `<li>${item.name} - $${item.totalPrice}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                } else {
                    itemsList.innerHTML = '<div class="text-muted">No items added yet</div>';
                }
            }
        }

        // Override generateReceipt to enable buttons
        const originalGenerateReceipt = window.generateReceipt;
        window.generateReceipt = function() {
            if (originalGenerateReceipt) {
                originalGenerateReceipt();
                // Enable save and print buttons
                document.getElementById('saveBtn').disabled = false;
                document.getElementById('printBtn').disabled = false;
            } else {
                alert('generateReceipt function not available');
            }
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateItemsDisplay();
            console.log('Available functions:', {
                ReceiptGenerator: !!window.ReceiptGenerator,
                ItemManager: !!window.ItemManager,
                UIManager: !!window.UIManager,
                generateReceipt: !!window.generateReceipt,
                saveReceipt: !!window.saveReceipt,
                printReceipt: !!window.printReceipt
            });
        });
    </script>
