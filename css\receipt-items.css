/**
 * Receipt Items Management Styles
 * Compact view and drag-and-drop functionality
 */

/* Receipt Items Container */
.receipt-items-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.5rem;
}

/* Individual Receipt Item */
.receipt-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem; /* Compact spacing */
    background: white;
    transition: all 0.2s ease;
    position: relative;
}

.receipt-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
}

.receipt-item:last-child {
    margin-bottom: 0;
}

/* Compact Item Layout */
.receipt-item .row {
    align-items: center;
    margin: 0;
}

.receipt-item .col-md-4,
.receipt-item .col-md-2 {
    padding: 0.25rem 0.5rem;
}

/* Price Information Display */
.item-price-info {
    font-size: 0.85rem;
}

.price-display {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.original-price {
    font-size: 0.8rem;
}

.special-price {
    font-size: 0.9rem;
    font-weight: bold;
}

.discount-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
}

/* Item Info Section */
.item-info h6 {
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
    font-weight: 600;
    color: #333;
}

.item-category {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.item-description {
    font-size: 0.8rem;
    color: #6c757d;
    margin: 0;
}

/* Form Controls in Items */
.receipt-item .form-label {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #495057;
}

.receipt-item .form-control-sm {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    height: auto;
}

/* Total Price Display */
.item-total-price {
    font-weight: bold;
    font-size: 0.95rem;
    color: #198754;
}

/* Action Buttons */
.item-actions {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.item-actions .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
}

/* Drag and Drop Styles */
.receipt-item.draggable {
    cursor: move;
}

.receipt-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    z-index: 1000;
}

.receipt-item.drag-over {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
    border-style: dashed;
}

/* Drag Handle */
.drag-handle {
    position: absolute;
    left: 0.25rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: move;
    color: #6c757d;
    font-size: 1rem;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.drag-handle:hover {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.receipt-item.draggable {
    padding-left: 2.5rem; /* Make room for drag handle */
}

/* Empty State */
.receipt-items-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.receipt-items-empty i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.receipt-items-empty p {
    margin: 0;
    font-size: 1rem;
}

/* Compact View Toggle */
.view-toggle {
    margin-bottom: 1rem;
}

.view-toggle .btn-group .btn {
    font-size: 0.85rem;
    padding: 0.375rem 0.75rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .receipt-item .row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .receipt-item .col-md-4,
    .receipt-item .col-md-2 {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .receipt-item .col-md-2:last-child {
        margin-bottom: 0;
    }
    
    .item-actions {
        flex-direction: row;
        justify-content: center;
    }
    
    .drag-handle {
        position: static;
        transform: none;
        align-self: center;
        margin-bottom: 0.5rem;
    }
    
    .receipt-item.draggable {
        padding-left: 0.75rem;
    }
}

/* Animation for item addition/removal */
.receipt-item.item-adding {
    animation: slideInDown 0.3s ease-out;
}

.receipt-item.item-removing {
    animation: slideOutUp 0.3s ease-in;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Sortable.js specific styles */
.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    cursor: grabbing;
}

.sortable-drag {
    opacity: 0.8;
    transform: rotate(5deg);
}

/* Item numbering for better UX */
.receipt-item::before {
    content: counter(item-counter);
    counter-increment: item-counter;
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: #e9ecef;
    color: #495057;
    font-size: 0.7rem;
    font-weight: bold;
    padding: 0.2rem 0.4rem;
    border-radius: 50%;
    min-width: 1.5rem;
    text-align: center;
    line-height: 1;
}

.receipt-items-container {
    counter-reset: item-counter;
}

/* Totals Display in Items Section */
.receipt-totals-summary {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.receipt-totals-summary .row {
    margin-bottom: 0.5rem;
    align-items: center;
}

.receipt-totals-summary .row:last-child {
    margin-bottom: 0;
    padding-top: 0.5rem;
    border-top: 2px solid #dee2e6;
    font-weight: bold;
    font-size: 1.1rem;
}

.receipt-totals-summary .col-6:first-child {
    text-align: right;
    font-weight: 600;
    color: #495057;
}

.receipt-totals-summary .col-6:last-child {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.receipt-totals-summary .text-danger {
    color: #dc3545 !important;
}

/* Form Input Styles */
.discount-input {
    width: 100px !important;
}

.tax-input {
    width: 80px !important;
}

/* Compact view specific styles */
.receipt-items-container.compact-view .receipt-item {
    margin-bottom: 0.25rem;
    padding: 0.5rem;
}

.receipt-items-container.compact-view .receipt-item .form-label {
    font-size: 0.75rem;
    margin-bottom: 0.125rem;
}

.receipt-items-container.compact-view .receipt-item .form-control-sm {
    font-size: 0.8rem;
    padding: 0.125rem 0.375rem;
}

/* Control buttons */
.receipt-items-controls {
    margin-bottom: 1rem;
}

.receipt-items-controls .btn-group .btn {
    font-size: 0.8rem;
}

/* Drag and drop visual feedback */
.receipt-item.sortable-chosen {
    opacity: 0.8;
}

.receipt-item.sortable-ghost {
    opacity: 0.4;
    background-color: #f8f9fa;
}

.receipt-item.sortable-drag {
    transform: rotate(2deg);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}
