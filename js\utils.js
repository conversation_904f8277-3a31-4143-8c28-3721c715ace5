/**
 * 工具函數模組
 * KMS PC Receipt Maker
 */

// 工具類
class Utils {
    /**
     * 顯示訊息
     */
    static showMessage(message, type = 'info', duration = 3000) {
        // 創建訊息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        messageDiv.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        messageDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(messageDiv);
        
        // 自動移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, duration);
    }

    /**
     * 格式化檔案大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 初始化日期時間
     */
    static initializeDatetime() {
        // 日期欄位預設為空白
        document.getElementById('receiptDate').value = '';
        
        // 設定預設收據編號
        document.getElementById('receiptNumber').value = 'KMS-UltraVIP-0000001';
    }

    /**
     * 初始化表單驗證
     */
    static initializeFormValidation() {
        // 客戶姓名驗證 - 移除必填驗證，允許空白
        const customerNameInput = document.getElementById('customerName');
        if (customerNameInput) {
            customerNameInput.addEventListener('blur', function() {
                // 移除必填驗證，客戶姓名可以為空
                this.classList.remove('is-invalid');
                if (this.value.trim() !== '') {
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                }
            });
        }

        // 電子郵件驗證
        const emailInput = document.getElementById('customerEmail');
        if (emailInput) {
            emailInput.addEventListener('blur', function() {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (this.value.trim() !== '' && !emailRegex.test(this.value)) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                    if (this.value.trim() !== '') {
                        this.classList.add('is-valid');
                    }
                }
            });
        }
    }

    /**
     * 計算總計
     */
    static calculateTotals() {
        let subtotal = 0;

        // 計算小計
        window.receiptItems.forEach(item => {
            subtotal += item.totalPrice || 0;
        });

        // 獲取折扣金額
        const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;

        // 計算稅費
        const taxRate = parseFloat(document.getElementById('taxRate').value) || 0;
        const taxAmount = Math.max(0, (subtotal - discountAmount) * (taxRate / 100));

        // 計算總計
        const totalAmount = Math.max(0, subtotal - discountAmount + taxAmount);

        // 更新顯示
        Utils.updateTotalsDisplay(subtotal, discountAmount, taxAmount, totalAmount);

        return {
            subtotal,
            discountAmount,
            taxAmount,
            totalAmount
        };
    }

    /**
     * 更新總計顯示
     */
    static updateTotalsDisplay(subtotal, discountAmount, taxAmount, totalAmount) {
        // 更新小計
        const subtotalElement = document.getElementById('subtotalAmount');
        if (subtotalElement) {
            subtotalElement.textContent = LanguageManager.formatCurrency(subtotal);
        }

        // 更新折扣
        const discountElement = document.getElementById('discountAmountDisplay');
        const discountRow = document.getElementById('discountRow');
        if (discountElement && discountRow) {
            discountElement.textContent = LanguageManager.formatCurrency(discountAmount);
            discountRow.style.display = discountAmount > 0 ? 'flex' : 'none';
        }

        // 更新稅費
        const taxElement = document.getElementById('taxAmountDisplay');
        const taxRow = document.getElementById('taxRow');
        if (taxElement && taxRow) {
            taxElement.textContent = LanguageManager.formatCurrency(taxAmount);
            taxRow.style.display = taxAmount > 0 ? 'flex' : 'none';
        }

        // 更新總計
        const totalElement = document.getElementById('totalAmount');
        if (totalElement) {
            totalElement.textContent = LanguageManager.formatCurrency(totalAmount);
        }
    }

    /**
     * 處理 Logo 上傳
     */
    static handleLogoUpload(event) {
        const file = event.target.files[0];
        if (!file) {
            Utils.hideLogo();
            return;
        }

        // 檢查檔案類型
        if (!file.type.startsWith('image/')) {
            alert(LanguageManager.getText('error_invalid_image') || '請選擇有效的圖片檔案');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            // 創建圖片對象以獲取尺寸
            const img = new Image();
            img.onload = function() {
                window.currentLogo = {
                    src: e.target.result,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    width: this.width,
                    height: this.height,
                    x: 20, // 預設位置
                    y: 20,
                    displayWidth: 100, // 顯示大小
                    displayHeight: 100
                };
                
                // 顯示預覽
                Utils.showLogoPreview(window.currentLogo, file);
                
                // 顯示成功訊息
                Utils.showMessage(LanguageManager.getText('logo_uploaded_success') || 'Logo 上傳成功！', 'success');
            };
            img.src = e.target.result;
        };
        
        reader.readAsDataURL(file);
    }

    /**
     * 顯示 Logo 預覽
     */
    static showLogoPreview(logoData, file) {
        const previewContainer = document.getElementById('logoPreview');
        const previewImage = document.getElementById('logoPreviewImage');
        const fileName = document.getElementById('logoFileName');
        const fileType = document.getElementById('logoFileType');
        const fileSize = document.getElementById('logoFileSize');
        const imageDimensions = document.getElementById('logoImageDimensions');
        
        // 設定預覽圖片
        previewImage.src = logoData.src;
        
        // 設定檔案資訊
        fileName.textContent = logoData.name;
        fileType.textContent = logoData.type;
        fileSize.textContent = Utils.formatFileSize(logoData.size);
        imageDimensions.textContent = `${logoData.width} × ${logoData.height} px`;
        
        // 顯示預覽容器
        previewContainer.style.display = 'block';
    }

    /**
     * 移除 Logo
     */
    static removeLogo() {
        window.currentLogo = null;
        document.getElementById('logoUpload').value = '';
        Utils.hideLogo();
        Utils.showMessage(LanguageManager.getText('logo_removed') || 'Logo 已移除', 'success');
    }

    /**
     * 隱藏 Logo 預覽
     */
    static hideLogo() {
        const previewContainer = document.getElementById('logoPreview');
        previewContainer.style.display = 'none';
    }

    /**
     * 綁定事件監聽器
     */
    static bindEventListeners() {
        // 折扣金額變更
        document.getElementById('discountAmount').addEventListener('input', Utils.calculateTotals);
        
        // 稅率變更
        document.getElementById('taxRate').addEventListener('input', Utils.calculateTotals);

        // 模態框項目表單事件
        if (document.getElementById('modalItemQuantity')) {
            document.getElementById('modalItemQuantity').addEventListener('input', calculateModalItemTotal);
            document.getElementById('modalItemOriginalPrice').addEventListener('input', calculateModalItemTotal);
            document.getElementById('modalItemSpecialPrice').addEventListener('input', calculateModalItemTotal);
        }

        // 語言變更事件
        document.addEventListener('languageChanged', function(event) {
            if (window.itemManager) {
                window.itemManager.updateItemsList();
            }
        });

        // 模態框關閉事件
        const addItemModal = document.getElementById('addItemModal');
        if (addItemModal) {
            addItemModal.addEventListener('hidden.bs.modal', function () {
                if (window.itemManager) {
                    window.itemManager.currentEditingIndex = -1;
                }
            });
        }
    }

    /**
     * 清空表單
     */
    static clearForm() {
        if (confirm(LanguageManager.getText('confirm_clear_form') || '確定要清空表單嗎？')) {
            // 清空客戶資訊
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';
            document.getElementById('customerEmail').value = '';
            document.getElementById('customerAddress').value = '';
            
            // 重置付款方式
            document.getElementById('paymentMethod').value = 'cash';
            
            // 清空項目
            window.receiptItems = [];
            if (window.itemManager) {
                window.itemManager.updateItemsList();
            }
            
            // 重置折扣和稅率
            document.getElementById('discountAmount').value = '0';
            document.getElementById('taxRate').value = '0';
            document.getElementById('notes').value = '';
            
            // 重新計算總計
            Utils.calculateTotals();
            
            // 重置日期和收據編號
            Utils.initializeDatetime();
            
            Utils.showMessage(LanguageManager.getText('form_cleared') || '表單已清空', 'success');
        }
    }
}

// 導出到全局作用域
window.showMessage = Utils.showMessage;
window.calculateTotals = Utils.calculateTotals;
window.handleLogoUpload = Utils.handleLogoUpload;
window.removeLogo = Utils.removeLogo;
window.clearForm = Utils.clearForm;
